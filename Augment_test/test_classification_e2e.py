"""
End-to-end tests for app.llm.classification module.

This module contains comprehensive end-to-end tests that validate the entire
classification pipeline with realistic scenarios and mocked AWS services.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, MagicMock, patch, AsyncMock
import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from app.llm.classification import main


class TestEndToEndClassificationPipeline:
    """Comprehensive end-to-end tests for the classification pipeline."""
    
    @pytest.mark.asyncio
    async def test_complete_invoice_classification_pipeline(self, mock_settings):
        """
        Test complete end-to-end pipeline for invoice classification.
        
        This test validates:
        1. S3 URI parsing and validation
        2. TextractProcessor initialization and document processing
        3. BedrockProcessor initialization and classification
        4. Result compilation and structure validation
        5. Execution metadata accuracy
        """
        # Realistic test data
        s3_uri = "s3://document-extraction-logistically/invoices/carrier_invoice_2023_12345.pdf"
        
        # Realistic Textract response (simplified but representative)
        textract_combined_text = """<page1>
Invoice #7014
NIMBLE TRANSPORT
Invoice Summary:
Invoice Amount: $1,300.00
Load Reference: D63-6613904
Date: Dec 15, 2022
Term: Net 30 days

Bill To:
Superior Transport & Logistics
Attn To: Operations Tracking

Payable To:
Centerpoint Logistics LLC, DBA Nimble Transport
835 South St. Paul Street, Kansas City, KS 66105
<EMAIL>
</page1>"""
        
        textract_response = {
            "DocumentMetadata": {"Pages": 1},
            "Blocks": [
                {
                    "BlockType": "PAGE",
                    "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}},
                    "Id": "page-1"
                },
                {
                    "BlockType": "LINE",
                    "Confidence": 99.7998046875,
                    "Text": "Invoice #7014",
                    "Geometry": {"BoundingBox": {"Width": 0.186, "Height": 0.017, "Left": 0.061, "Top": 0.031}},
                    "Id": "line-1"
                },
                {
                    "BlockType": "LINE",
                    "Confidence": 99.970703125,
                    "Text": "$1,300.00",
                    "Geometry": {"BoundingBox": {"Width": 0.089, "Height": 0.014, "Left": 0.865, "Top": 0.099}},
                    "Id": "line-2"
                }
            ]
        }
        
        # Expected classification result
        expected_classification = {
            "documents": [
                {
                    "page_no": 1,
                    "doc_type": "invoice"
                }
            ]
        }
        
        # Realistic Bedrock response
        bedrock_response = {
            "output": {
                "message": {
                    "role": "assistant",
                    "content": [
                        {
                            "text": "I'll analyze this document to classify its type based on the content and keywords present."
                        },
                        {
                            "toolUse": {
                                "toolUseId": "classify-001",
                                "name": "classify_logistics_doc_type",
                                "input": expected_classification
                            }
                        }
                    ]
                }
            },
            "stopReason": "tool_use",
            "usage": {
                "inputTokens": 1247,
                "outputTokens": 89,
                "totalTokens": 1336
            }
        }
        
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup TextractProcessor mock
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (
                textract_combined_text,
                [textract_response]
            )
            
            # Setup BedrockProcessor mock
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = bedrock_response
            mock_bedrock.extract_tool_response.return_value = expected_classification
            
            # Execute the complete pipeline
            result = await main(s3_uri)
            
            # Validate pipeline execution order and calls
            mock_textract_class.assert_called_once()
            mock_bedrock_class.assert_called_once_with(aws_region=mock_settings.AWS_REGION_CLASSIFICATION)
            mock_textract.process_document_classification.assert_called_once_with(
                s3_uri, mock_settings.S3_BUCKET_NAME, mock_settings.TEXTRACT_TEMP_PREFIX
            )
            mock_bedrock.call_bedrock_converse.assert_called_once()
            mock_bedrock.extract_tool_response.assert_called_once_with(bedrock_response)
            
            # Validate result structure completeness
            assert isinstance(result, dict)
            required_top_level_keys = ['classification_result', 'textract_response_object', 
                                     'bedrock_response_object', 'execution_info']
            for key in required_top_level_keys:
                assert key in result, f"Missing required top-level key: {key}"
            
            # Validate classification result
            classification = result['classification_result']
            assert classification == expected_classification
            assert len(classification['documents']) == 1
            assert classification['documents'][0]['page_no'] == 1
            assert classification['documents'][0]['doc_type'] == 'invoice'
            
            # Validate Textract response preservation
            assert result['textract_response_object'] == [textract_response]
            assert result['textract_response_object'][0]['DocumentMetadata']['Pages'] == 1
            
            # Validate Bedrock response preservation
            assert result['bedrock_response_object'] == bedrock_response
            assert result['bedrock_response_object']['stopReason'] == 'tool_use'
            
            # Validate execution info completeness and accuracy
            exec_info = result['execution_info']
            assert 'textract' in exec_info
            assert 'bedrock' in exec_info
            
            # Textract execution info validation
            textract_info = exec_info['textract']
            assert textract_info['aws_region'] == mock_settings.AWS_REGION_EXTRACTION
            assert textract_info['file_path'] == s3_uri
            assert textract_info['bucket_name'] == mock_settings.S3_BUCKET_NAME
            
            # Bedrock execution info validation
            bedrock_info = exec_info['bedrock']
            assert bedrock_info['aws_region'] == mock_settings.AWS_REGION_CLASSIFICATION
            assert bedrock_info['model_id'] == mock_settings.MODEL_ID_CLASSIFICATION
            assert bedrock_info['user_prompt'] == textract_combined_text
            assert bedrock_info['temperature'] == mock_settings.TEMPERATURE_CLASSIFICATION
            assert bedrock_info['max_tokens'] == mock_settings.MAX_TOKENS_CLASSIFICATION
            assert bedrock_info['reasoning_effort'] == mock_settings.REASONING_EFFORT_CLASSIFICATION
            
            # Validate that system prompt and tool call are included
            assert 'system_prompt' in bedrock_info
            assert 'tool_call' in bedrock_info
            assert bedrock_info['system_prompt'] is not None
            assert bedrock_info['tool_call'] is not None
    
    @pytest.mark.asyncio
    async def test_multi_page_mixed_document_classification(self, mock_settings):
        """
        Test end-to-end pipeline for multi-page document with mixed document types.
        
        This test validates:
        1. Multi-page document processing
        2. Mixed document type classification
        3. Page-by-page classification accuracy
        4. Complex document structure handling
        """
        s3_uri = "s3://document-extraction-logistically/mixed/shipment_package_001.pdf"
        
        # Multi-page combined text
        multi_page_text = """<page1>
Invoice #INV-2023-001
ACME Logistics Inc.
Invoice Amount: $2,500.00
Bill To: XYZ Manufacturing
Date: January 15, 2023
</page1>

<page2>
PROOF OF DELIVERY
Delivery Date: January 18, 2023
Delivered To: John Smith, Receiving Manager
Signature: [Signed]
Condition: Good
POD #: POD-2023-001
</page2>

<page3>
BILL OF LADING
B/L Number: BL-2023-001
Shipper: ACME Logistics Inc.
Consignee: XYZ Manufacturing
Cargo Description: Electronic Components
Weight: 1,250 lbs
</page3>"""
        
        # Multi-page Textract responses
        textract_responses = [
            {"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Id": "page-1"}]},
            {"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Id": "page-2"}]},
            {"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Id": "page-3"}]}
        ]
        
        # Expected multi-page classification
        expected_classification = {
            "documents": [
                {"page_no": 1, "doc_type": "invoice"},
                {"page_no": 2, "doc_type": "pod"},
                {"page_no": 3, "doc_type": "bol"}
            ]
        }
        
        bedrock_response = {
            "output": {
                "message": {
                    "role": "assistant",
                    "content": [
                        {"text": "Analyzing multi-page document with different document types on each page."},
                        {
                            "toolUse": {
                                "toolUseId": "classify-multi-001",
                                "name": "classify_logistics_doc_type",
                                "input": expected_classification
                            }
                        }
                    ]
                }
            },
            "stopReason": "tool_use",
            "usage": {"inputTokens": 2156, "outputTokens": 145, "totalTokens": 2301}
        }
        
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup mocks
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (
                multi_page_text, textract_responses
            )
            
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = bedrock_response
            mock_bedrock.extract_tool_response.return_value = expected_classification
            
            # Execute pipeline
            result = await main(s3_uri)
            
            # Validate multi-page classification
            classification = result['classification_result']
            assert len(classification['documents']) == 3
            
            # Validate each page classification
            page_1 = classification['documents'][0]
            assert page_1['page_no'] == 1
            assert page_1['doc_type'] == 'invoice'
            
            page_2 = classification['documents'][1]
            assert page_2['page_no'] == 2
            assert page_2['doc_type'] == 'pod'
            
            page_3 = classification['documents'][2]
            assert page_3['page_no'] == 3
            assert page_3['doc_type'] == 'bol'
            
            # Validate that all Textract responses are preserved
            assert len(result['textract_response_object']) == 3
            for i, response in enumerate(result['textract_response_object']):
                assert response['DocumentMetadata']['Pages'] == 1
                assert f"page-{i+1}" in str(response)
            
            # Validate that multi-page text was passed to Bedrock
            bedrock_call_args = mock_bedrock.call_bedrock_converse.call_args
            user_message = bedrock_call_args[1]['messages'][0]['content'][0]['text']
            assert "<page1>" in user_message
            assert "<page2>" in user_message
            assert "<page3>" in user_message
            assert "Invoice #INV-2023-001" in user_message
            assert "PROOF OF DELIVERY" in user_message
            assert "BILL OF LADING" in user_message
    
    @pytest.mark.asyncio
    async def test_image_document_classification_pipeline(self, mock_settings):
        """
        Test end-to-end pipeline for image document classification.
        
        This test validates:
        1. Image file processing (JPG/PNG/TIFF)
        2. Single-page image classification
        3. Image-specific Textract handling
        """
        s3_uri = "s3://document-extraction-logistically/images/fuel_receipt_001.jpg"
        
        # Image document text
        image_text = """<page1>
SHELL FUEL STATION
Receipt #: 789456123
Date: 2023-01-20 14:30:25
Pump #: 3
Fuel Type: Diesel
Gallons: 45.2
Price per Gallon: $3.89
Total Amount: $175.83
Payment Method: Fleet Card
</page1>"""
        
        textract_response = {
            "DocumentMetadata": {"Pages": 1},
            "Blocks": [
                {"BlockType": "PAGE", "Id": "page-1"},
                {"BlockType": "LINE", "Text": "SHELL FUEL STATION", "Confidence": 99.5},
                {"BlockType": "LINE", "Text": "Gallons: 45.2", "Confidence": 98.7},
                {"BlockType": "LINE", "Text": "Total Amount: $175.83", "Confidence": 99.2}
            ]
        }
        
        expected_classification = {
            "documents": [
                {"page_no": 1, "doc_type": "fuel_receipt"}
            ]
        }
        
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup mocks
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (
                image_text, [textract_response]
            )
            
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {
                "output": {"message": {"content": [{"toolUse": {"input": expected_classification}}]}},
                "stopReason": "tool_use"
            }
            mock_bedrock.extract_tool_response.return_value = expected_classification
            
            # Execute pipeline
            result = await main(s3_uri)
            
            # Validate image processing
            assert result['classification_result']['documents'][0]['doc_type'] == 'fuel_receipt'
            
            # Validate that image-specific keywords were processed
            bedrock_call_args = mock_bedrock.call_bedrock_converse.call_args
            user_message = bedrock_call_args[1]['messages'][0]['content'][0]['text']
            assert "SHELL FUEL STATION" in user_message
            assert "Gallons:" in user_message
            assert "Fuel" in user_message or "fuel" in user_message
    
    @pytest.mark.asyncio
    async def test_classification_result_schema_validation(self, mock_settings):
        """
        Test that classification results conform to expected schema.
        
        This test validates:
        1. Classification result schema compliance
        2. Required field presence
        3. Data type validation
        4. Value range validation
        """
        s3_uri = "s3://document-extraction-logistically/test/schema_validation.pdf"
        
        # Test with various document types to validate schema consistency
        test_cases = [
            {"doc_type": "invoice", "page_no": 1},
            {"doc_type": "bol", "page_no": 1},
            {"doc_type": "pod", "page_no": 2},
            {"doc_type": "scale_ticket", "page_no": 3},
            {"doc_type": "other", "page_no": 4}
        ]
        
        for test_case in test_cases:
            classification_result = {"documents": [test_case]}
            
            with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
                 patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
                
                # Setup mocks
                mock_textract = MagicMock()
                mock_textract_class.return_value = mock_textract
                mock_textract.process_document_classification.return_value = (
                    f"<page{test_case['page_no']}>\nTest content\n</page{test_case['page_no']}>",
                    [{"DocumentMetadata": {"Pages": 1}}]
                )
                
                mock_bedrock = MagicMock()
                mock_bedrock_class.return_value = mock_bedrock
                mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
                mock_bedrock.extract_tool_response.return_value = classification_result
                
                # Execute
                result = await main(s3_uri)
                
                # Validate schema compliance
                classification = result['classification_result']
                
                # Validate top-level structure
                assert isinstance(classification, dict)
                assert 'documents' in classification
                assert isinstance(classification['documents'], list)
                assert len(classification['documents']) == 1
                
                # Validate document structure
                document = classification['documents'][0]
                assert isinstance(document, dict)
                
                # Validate required fields
                assert 'page_no' in document
                assert 'doc_type' in document
                
                # Validate field types
                assert isinstance(document['page_no'], int)
                assert isinstance(document['doc_type'], str)
                
                # Validate field values
                assert document['page_no'] > 0  # Page numbers should be positive
                assert len(document['doc_type']) > 0  # Doc type should not be empty
                
                # Validate that doc_type is from expected enum values
                valid_doc_types = [
                    "invoice", "comm_invoice", "lumper_receipt", "bol", "pod", 
                    "rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", 
                    "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", 
                    "combined_carrier_documents", "customs_doc", "other", "so_confirmation", 
                    "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"
                ]
                assert document['doc_type'] in valid_doc_types
