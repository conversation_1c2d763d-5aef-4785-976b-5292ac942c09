#!/usr/bin/env python3
"""
Real integration test runner for classification.py.

This script runs integration tests that use actual AWS services and real test documents.
It requires proper AWS credentials and access to the S3 bucket.
"""

import sys
import subprocess
import argparse
import os
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))


def check_aws_credentials():
    """Check if AWS credentials are configured."""
    # Check if .env file exists (credentials should be there)
    env_file = project_root / ".env"
    if env_file.exists():
        print("✅ .env file found - AWS credentials should be configured")
        return True
    else:
        print("⚠️  .env file not found - AWS credentials may not be configured")
        print("   The classification.py module will handle credential loading")
        return True  # Don't fail, let classification.py handle it


def check_test_data():
    """Check if test data directories exist."""
    input_dir = project_root / "tests" / "classification_input_data"
    output_dir = project_root / "tests" / "classification_output_data"
    
    if not input_dir.exists():
        print(f"❌ Test input directory not found: {input_dir}")
        return False
    
    if not output_dir.exists():
        print(f"❌ Test output directory not found: {output_dir}")
        return False
    
    input_files = list(input_dir.glob("*"))
    output_files = list(output_dir.glob("*.json"))
    
    print(f"✅ Test data found:")
    print(f"   - Input files: {len(input_files)}")
    print(f"   - Expected output files: {len(output_files)}")
    
    return len(input_files) > 0 and len(output_files) > 0


def run_real_integration_tests(test_type="all", verbose=False, specific_doc=None):
    """
    Run real integration tests with specified options.
    
    Args:
        test_type (str): Type of tests to run ("single", "batch", "images", "structure", "all")
        verbose (bool): Enable verbose output
        specific_doc (str): Run test for specific document type
    """
    test_dir = Path(__file__).parent
    test_file = test_dir / "test_classification_real_integration.py"
    
    # Build pytest command
    cmd = ["python", "-m", "pytest", str(test_file)]
    
    # Add test selection based on type
    if test_type == "single":
        cmd.extend(["-k", "test_single_document_classification"])
    elif test_type == "batch":
        cmd.extend(["-k", "test_batch_classification_accuracy"])
    elif test_type == "images":
        cmd.extend(["-k", "test_image_document_classification"])
    elif test_type == "structure":
        cmd.extend(["-k", "test_classification_result_structure_validation"])
    elif test_type == "specific" and specific_doc:
        cmd.extend(["-k", f"test_specific_document_types and {specific_doc}"])
    # For "all", run all integration tests
    
    # Add markers
    cmd.extend(["-m", "integration"])
    
    # Add options
    if verbose:
        cmd.extend(["-v", "-s"])
    else:
        cmd.append("-q")
    
    # Add other useful options
    cmd.extend([
        "--tb=short",
        "--disable-warnings",
        "--maxfail=3"  # Stop after 3 failures
    ])
    
    print(f"Running {test_type} real integration tests...")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 60)
    
    # Run tests
    try:
        result = subprocess.run(cmd, cwd=project_root, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return 130
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def main():
    """Main entry point for real integration test runner."""
    parser = argparse.ArgumentParser(description="Run real integration tests for classification.py")
    parser.add_argument(
        "test_type",
        nargs="?",
        default="single",
        choices=["single", "batch", "images", "structure", "specific", "all"],
        help="Type of tests to run (default: single)"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Enable verbose output"
    )
    parser.add_argument(
        "--doc-type",
        help="Specific document type to test (use with 'specific' test type)"
    )
    parser.add_argument(
        "--skip-checks",
        action="store_true",
        help="Skip environment and data checks"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be run without actually running tests"
    )
    
    args = parser.parse_args()
    
    print("🧪 Real Integration Test Runner for Classification")
    print("=" * 50)
    
    # Skip checks if requested
    if not args.skip_checks:
        print("Checking prerequisites...")
        
        # Check AWS credentials
        if not check_aws_credentials():
            return 1
        
        # Check test data
        if not check_test_data():
            return 1
        
        print()
    
    # Handle specific document type
    if args.test_type == "specific" and not args.doc_type:
        print("❌ --doc-type is required when using 'specific' test type")
        print("Available document types: invoice, bol, pod, scale_ticket, fuel_receipt, lumper_receipt")
        return 1
    
    # Dry run
    if args.dry_run:
        print("🔍 Dry run mode - showing what would be executed:")
        print(f"Test type: {args.test_type}")
        if args.doc_type:
            print(f"Document type: {args.doc_type}")
        print(f"Verbose: {args.verbose}")
        return 0
    
    # Show warning about real AWS usage
    print("⚠️  WARNING: These tests will use real AWS services!")
    print("   - Files will be uploaded to S3")
    print("   - Textract and Bedrock APIs will be called")
    print("   - This may incur AWS charges")
    print()
    
    # Ask for confirmation unless running single test
    if args.test_type in ["batch", "all"]:
        response = input("Do you want to continue? (y/N): ")
        if response.lower() != 'y':
            print("Tests cancelled by user")
            return 0
    
    # Run tests
    return run_real_integration_tests(
        test_type=args.test_type,
        verbose=args.verbose,
        specific_doc=args.doc_type
    )


if __name__ == "__main__":
    sys.exit(main())
