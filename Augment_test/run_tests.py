#!/usr/bin/env python3
"""
Test runner for classification.py tests.

This script provides a convenient way to run all classification tests
with proper configuration and reporting.
"""

import sys
import subprocess
import argparse
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))


def run_tests(test_type="all", verbose=False, coverage=False):
    """
    Run classification tests with specified options.
    
    Args:
        test_type (str): Type of tests to run ("unit", "integration", "e2e", "errors", "all")
        verbose (bool): Enable verbose output
        coverage (bool): Enable coverage reporting
    """
    test_dir = Path(__file__).parent
    
    # Define test file mappings
    test_files = {
        "unit": "test_classification_unit.py",
        "integration": "test_classification_integration.py", 
        "e2e": "test_classification_e2e.py",
        "errors": "test_classification_errors.py",
        "all": "test_classification_*.py"
    }
    
    if test_type not in test_files:
        print(f"Error: Invalid test type '{test_type}'. Valid options: {list(test_files.keys())}")
        return 1
    
    # Build pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add test files
    if test_type == "all":
        cmd.extend([str(test_dir / "test_classification_unit.py")])
        cmd.extend([str(test_dir / "test_classification_integration.py")])
        cmd.extend([str(test_dir / "test_classification_e2e.py")])
        cmd.extend([str(test_dir / "test_classification_errors.py")])
    else:
        cmd.append(str(test_dir / test_files[test_type]))
    
    # Add options
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # Add coverage if requested
    if coverage:
        cmd.extend([
            "--cov=app.llm.classification",
            "--cov-report=html:Augment_test/htmlcov",
            "--cov-report=term-missing"
        ])
    
    # Add async support
    cmd.extend(["-p", "no:warnings"])
    cmd.append("--tb=short")
    
    print(f"Running {test_type} tests...")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 50)
    
    # Run tests
    try:
        result = subprocess.run(cmd, cwd=project_root, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return 130
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(description="Run classification.py tests")
    parser.add_argument(
        "test_type",
        nargs="?",
        default="all",
        choices=["unit", "integration", "e2e", "errors", "all"],
        help="Type of tests to run (default: all)"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Enable verbose output"
    )
    parser.add_argument(
        "-c", "--coverage",
        action="store_true",
        help="Enable coverage reporting"
    )
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install test dependencies before running tests"
    )
    
    args = parser.parse_args()
    
    # Install dependencies if requested
    if args.install_deps:
        print("Installing test dependencies...")
        deps_cmd = [
            "pip", "install", 
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "pytest-mock>=3.10.0"
        ]
        try:
            subprocess.run(deps_cmd, check=True)
            print("Dependencies installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"Error installing dependencies: {e}")
            return 1
    
    # Run tests
    return run_tests(args.test_type, args.verbose, args.coverage)


if __name__ == "__main__":
    sys.exit(main())
