"""
Error handling and edge case tests for app.llm.classification module.

This module contains tests for various error scenarios, malformed inputs,
service failures, and edge cases in the classification pipeline.
"""

import pytest
import asyncio
from unittest.mock import Mock, MagicMock, patch, AsyncMock
import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from app.llm.classification import main, get_document_types_with_bedrock


class TestInputValidationErrors:
    """Test cases for input validation and malformed inputs."""
    
    @pytest.mark.asyncio
    async def test_none_file_path(self, mock_settings):
        """Test handling of None file path."""
        with pytest.raises(ValueError, match="file_path must be a non-empty string"):
            await main(None)
    
    @pytest.mark.asyncio
    async def test_empty_string_file_path(self, mock_settings):
        """Test handling of empty string file path."""
        with pytest.raises(ValueError, match="file_path must be a non-empty string"):
            await main("")
    
    @pytest.mark.asyncio
    async def test_non_string_file_path(self, mock_settings):
        """Test handling of non-string file path."""
        with pytest.raises(ValueError, match="file_path must be a non-empty string"):
            await main(123)
        
        with pytest.raises(ValueError, match="file_path must be a non-empty string"):
            await main(['s3://bucket/file.pdf'])
    
    @pytest.mark.asyncio
    async def test_invalid_s3_uri_schemes(self, mock_settings):
        """Test handling of invalid S3 URI schemes."""
        invalid_uris = [
            "http://bucket/file.pdf",
            "https://bucket/file.pdf",
            "ftp://bucket/file.pdf",
            "file:///local/path/file.pdf",
            "/local/path/file.pdf",
            "bucket/file.pdf"
        ]
        
        for uri in invalid_uris:
            with pytest.raises(ValueError, match="Only S3 URIs are supported"):
                await main(uri)
    
    @pytest.mark.asyncio
    async def test_malformed_s3_uris(self, mock_settings):
        """Test handling of malformed S3 URIs."""
        malformed_uris = [
            "s3://",
            "s3:///file.pdf",
            "s3://bucket",
            "s3://bucket/",
        ]

        # These should raise ValueError for malformed S3 URIs
        for uri in malformed_uris:
            with pytest.raises(ValueError, match="Failed to parse S3 URI"):
                await main(uri)


class TestTextractServiceErrors:
    """Test cases for Textract service errors and failures."""
    
    @pytest.mark.asyncio
    async def test_textract_service_unavailable(self, mock_settings, error_scenarios):
        """Test handling of Textract service unavailability."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class:
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.side_effect = error_scenarios['textract_error']
            
            with pytest.raises(Exception, match="Textract service unavailable"):
                await main("s3://bucket/file.pdf")
    
    @pytest.mark.asyncio
    async def test_textract_permission_denied(self, mock_settings, error_scenarios):
        """Test handling of Textract permission errors."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class:
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.side_effect = error_scenarios['permission_error']
            
            with pytest.raises(PermissionError, match="Access denied"):
                await main("s3://bucket/file.pdf")
    
    @pytest.mark.asyncio
    async def test_textract_timeout(self, mock_settings, error_scenarios):
        """Test handling of Textract timeout errors."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class:
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.side_effect = error_scenarios['timeout_error']
            
            with pytest.raises(TimeoutError, match="Request timed out"):
                await main("s3://bucket/file.pdf")
    
    @pytest.mark.asyncio
    async def test_textract_empty_response(self, mock_settings):
        """Test handling of empty Textract response."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup Textract to return empty response
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = ("", [])
            
            # Setup Bedrock
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
            mock_bedrock.extract_tool_response.return_value = {"documents": []}
            
            # Execute - should not raise error but handle gracefully
            result = await main("s3://bucket/file.pdf")
            
            # Verify that empty text was processed
            assert result['classification_result']['documents'] == []
    
    @pytest.mark.asyncio
    async def test_textract_malformed_response(self, mock_settings):
        """Test handling of malformed Textract response."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup Textract to return malformed response
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (
                "corrupted text with \x00 null bytes and \xff invalid chars",
                [{"malformed": "response", "missing_required_fields": True}]
            )
            
            # Setup Bedrock
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
            mock_bedrock.extract_tool_response.return_value = {"documents": [{"page_no": 1, "doc_type": "other"}]}
            
            # Execute - should handle malformed text gracefully
            result = await main("s3://bucket/file.pdf")
            
            # Verify processing continued despite malformed input
            assert 'classification_result' in result
            assert result['textract_response_object'][0]['malformed'] == "response"


class TestBedrockServiceErrors:
    """Test cases for Bedrock service errors and failures."""
    
    @pytest.mark.asyncio
    async def test_bedrock_model_not_found(self, mock_settings, error_scenarios):
        """Test handling of Bedrock model not found error."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup Textract
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = ("test text", [{}])
            
            # Setup Bedrock to fail
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.side_effect = error_scenarios['bedrock_error']
            
            with pytest.raises(Exception, match="Bedrock model not found"):
                await main("s3://bucket/file.pdf")
    
    @pytest.mark.asyncio
    async def test_bedrock_max_tokens_reached(self, mock_settings):
        """Test handling of Bedrock max tokens reached."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup Textract
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = ("test text", [{}])
            
            # Setup Bedrock to return max tokens response
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {
                "output": {"message": {"content": [{"text": "partial response"}]}},
                "stopReason": "max_tokens"
            }
            mock_bedrock.extract_tool_response.side_effect = Exception(
                "Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file"
            )
            
            with pytest.raises(Exception, match="Max tokens reached"):
                await main("s3://bucket/file.pdf")
    
    @pytest.mark.asyncio
    async def test_bedrock_malformed_response(self, mock_settings, error_scenarios):
        """Test handling of malformed Bedrock response."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup Textract
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = ("test text", [{}])
            
            # Setup Bedrock to return malformed response
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = error_scenarios['invalid_response']
            mock_bedrock.extract_tool_response.side_effect = Exception("Unexpected tool response format from Bedrock")
            
            with pytest.raises(Exception, match="Unexpected tool response format from Bedrock"):
                await main("s3://bucket/file.pdf")
    
    @pytest.mark.asyncio
    async def test_bedrock_empty_classification_result(self, mock_settings):
        """Test handling of empty classification result from Bedrock."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup Textract
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = ("test text", [{}])
            
            # Setup Bedrock to return empty classification
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
            mock_bedrock.extract_tool_response.return_value = {"documents": []}
            
            # Execute - should handle empty classification gracefully
            result = await main("s3://bucket/file.pdf")
            
            # Verify empty result is handled
            assert result['classification_result']['documents'] == []


class TestEdgeCasesAndBoundaryConditions:
    """Test cases for edge cases and boundary conditions."""
    
    @pytest.mark.asyncio
    async def test_very_large_document_text(self, mock_settings):
        """Test handling of very large document text."""
        # Create a very large text string (simulate large document)
        large_text = "<page1>\n" + "A" * 100000 + "\n</page1>"
        
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup Textract to return large text
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (large_text, [{}])
            
            # Setup Bedrock
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
            mock_bedrock.extract_tool_response.return_value = {"documents": [{"page_no": 1, "doc_type": "other"}]}
            
            # Execute - should handle large text
            result = await main("s3://bucket/large_file.pdf")
            
            # Verify large text was passed to Bedrock
            bedrock_call_args = mock_bedrock.call_bedrock_converse.call_args
            user_message = bedrock_call_args[1]['messages'][0]['content'][0]['text']
            assert len(user_message) > 100000
            assert result['classification_result']['documents'][0]['doc_type'] == 'other'
    
    @pytest.mark.asyncio
    async def test_special_characters_in_text(self, mock_settings):
        """Test handling of special characters and unicode in document text."""
        # Text with various special characters and unicode
        special_text = "<page1>\nInvoice #123\n€1,000.00\n中文字符\n🚚 Delivery\nTab\tCharacter\nNewline\nCharacter\n</page1>"
        
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup Textract
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (special_text, [{}])
            
            # Setup Bedrock
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
            mock_bedrock.extract_tool_response.return_value = {"documents": [{"page_no": 1, "doc_type": "invoice"}]}
            
            # Execute
            result = await main("s3://bucket/special_chars.pdf")
            
            # Verify special characters were preserved
            bedrock_call_args = mock_bedrock.call_bedrock_converse.call_args
            user_message = bedrock_call_args[1]['messages'][0]['content'][0]['text']
            assert "€1,000.00" in user_message
            assert "中文字符" in user_message
            assert "🚚 Delivery" in user_message
    
    @pytest.mark.asyncio
    async def test_classification_confidence_edge_cases(self, mock_settings):
        """Test handling of edge cases in classification confidence."""
        # Test with classification result that has unusual confidence values
        unusual_classification = {
            "documents": [
                {"page_no": 1, "doc_type": "other", "confidence": 0.0},  # Zero confidence
                {"page_no": 2, "doc_type": "invoice", "confidence": 1.0},  # Perfect confidence
                {"page_no": 3, "doc_type": "unknown"}  # Missing confidence
            ]
        }
        
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup mocks
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (
                "<page1>\nPage 1\n</page1>\n<page2>\nPage 2\n</page2>\n<page3>\nPage 3\n</page3>",
                [{}, {}, {}]
            )
            
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
            mock_bedrock.extract_tool_response.return_value = unusual_classification
            
            # Execute - should handle unusual confidence values
            result = await main("s3://bucket/confidence_test.pdf")
            
            # Verify all documents were processed despite unusual confidence values
            docs = result['classification_result']['documents']
            assert len(docs) == 3
            assert docs[0]['doc_type'] == 'other'
            assert docs[1]['doc_type'] == 'invoice'
            assert docs[2]['doc_type'] == 'unknown'
    
    @pytest.mark.asyncio
    async def test_concurrent_execution_safety(self, mock_settings):
        """Test that concurrent executions don't interfere with each other."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup mocks that return different results for different calls
            call_count = 0
            
            def textract_side_effect(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                return (f"<page1>\nDocument {call_count}\n</page1>", [{"call": call_count}])
            
            def bedrock_side_effect(*args, **kwargs):
                return {"documents": [{"page_no": 1, "doc_type": f"type_{call_count}"}]}
            
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.side_effect = textract_side_effect
            
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
            mock_bedrock.extract_tool_response.side_effect = bedrock_side_effect
            
            # Execute multiple concurrent calls
            tasks = [
                main(f"s3://bucket/file{i}.pdf")
                for i in range(3)
            ]
            
            results = await asyncio.gather(*tasks)
            
            # Verify each call got unique results
            assert len(results) == 3
            for i, result in enumerate(results):
                # Each result should have unique classification
                assert 'classification_result' in result
                # Note: Due to mocking, the exact doc_type might not be predictable
                # but we can verify structure integrity
                assert 'documents' in result['classification_result']
