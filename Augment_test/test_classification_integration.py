"""
Integration tests for app.llm.classification module.

This module contains integration tests that test the full classification pipeline
with mocked AWS services but real data flow between components.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, MagicMock, patch, AsyncMock
import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from app.llm.classification import main


class TestClassificationPipelineIntegration:
    """Integration tests for the complete classification pipeline."""
    
    @pytest.mark.asyncio
    async def test_full_pipeline_single_page_invoice(self, mock_settings, classification_test_data):
        """Test complete pipeline for single-page invoice classification."""
        # Setup test data
        invoice_data = classification_test_data.get('invoice', {
            "classification_result": {"documents": [{"page_no": 1, "doc_type": "invoice"}]},
            "textract_response_object": [{"DocumentMetadata": {"Pages": 1}}],
            "bedrock_response_object": {"output": {"message": {"content": []}}}
        })
        
        combined_text = "<page1>\nInvoice #7014\nNIMBLE TRANSPORT\nInvoice Amount: $1,300.00\n</page1>"
        
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup TextractProcessor mock
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (
                combined_text,
                invoice_data['textract_response_object']
            )
            
            # Setup BedrockProcessor mock
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = invoice_data['bedrock_response_object']
            mock_bedrock.extract_tool_response.return_value = invoice_data['classification_result']
            
            # Execute
            result = await main("s3://document-extraction-logistically/test/invoice.pdf")
            
            # Verify pipeline execution
            mock_textract.process_document_classification.assert_called_once()
            mock_bedrock.call_bedrock_converse.assert_called_once()
            mock_bedrock.extract_tool_response.assert_called_once()
            
            # Verify result structure and content
            assert result['classification_result'] == invoice_data['classification_result']
            assert len(result['textract_response_object']) == 1
            assert result['bedrock_response_object'] == invoice_data['bedrock_response_object']
            
            # Verify classification result
            docs = result['classification_result']['documents']
            assert len(docs) == 1
            assert docs[0]['page_no'] == 1
            assert docs[0]['doc_type'] == 'invoice'
    
    @pytest.mark.asyncio
    async def test_full_pipeline_multi_page_document(self, mock_settings, sample_multi_page_classification_result, 
                                                    sample_multi_page_combined_text):
        """Test complete pipeline for multi-page document classification."""
        # Setup multi-page textract responses
        textract_responses = [
            {"DocumentMetadata": {"Pages": 1}, "Blocks": []},
            {"DocumentMetadata": {"Pages": 1}, "Blocks": []},
            {"DocumentMetadata": {"Pages": 1}, "Blocks": []}
        ]
        
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup mocks
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (
                sample_multi_page_combined_text,
                textract_responses
            )
            
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
            mock_bedrock.extract_tool_response.return_value = sample_multi_page_classification_result
            
            # Execute
            result = await main("s3://document-extraction-logistically/test/multi_page.pdf")
            
            # Verify multi-page classification
            docs = result['classification_result']['documents']
            assert len(docs) == 3
            assert docs[0]['doc_type'] == 'invoice'
            assert docs[1]['doc_type'] == 'pod'
            assert docs[2]['doc_type'] == 'bol'
            
            # Verify page numbers are sequential
            for i, doc in enumerate(docs):
                assert doc['page_no'] == i + 1
    
    @pytest.mark.asyncio
    async def test_pipeline_with_different_document_types(self, mock_settings, classification_test_data):
        """Test pipeline with various document types."""
        document_types = ['bol', 'pod', 'scale_ticket', 'fuel_receipt', 'lumper_receipt']
        
        for doc_type in document_types:
            test_data = classification_test_data.get(doc_type, {
                "classification_result": {"documents": [{"page_no": 1, "doc_type": doc_type}]},
                "textract_response_object": [{"DocumentMetadata": {"Pages": 1}}],
                "bedrock_response_object": {"output": {"message": {"content": []}}}
            })
            
            combined_text = f"<page1>\nSample {doc_type} document content\n</page1>"
            
            with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
                 patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
                
                # Setup mocks
                mock_textract = MagicMock()
                mock_textract_class.return_value = mock_textract
                mock_textract.process_document_classification.return_value = (
                    combined_text,
                    test_data['textract_response_object']
                )
                
                mock_bedrock = MagicMock()
                mock_bedrock_class.return_value = mock_bedrock
                mock_bedrock.call_bedrock_converse.return_value = test_data['bedrock_response_object']
                mock_bedrock.extract_tool_response.return_value = test_data['classification_result']
                
                # Execute
                result = await main(f"s3://document-extraction-logistically/test/{doc_type}.pdf")
                
                # Verify classification
                assert result['classification_result']['documents'][0]['doc_type'] == doc_type
    
    @pytest.mark.asyncio
    async def test_pipeline_with_image_files(self, mock_settings):
        """Test pipeline with image file formats."""
        image_formats = ['jpg', 'png', 'tiff']
        
        for format_ext in image_formats:
            combined_text = f"<page1>\nImage document content from {format_ext}\n</page1>"
            classification_result = {"documents": [{"page_no": 1, "doc_type": "other"}]}
            
            with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
                 patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
                
                # Setup mocks
                mock_textract = MagicMock()
                mock_textract_class.return_value = mock_textract
                mock_textract.process_document_classification.return_value = (
                    combined_text,
                    [{"DocumentMetadata": {"Pages": 1}}]
                )
                
                mock_bedrock = MagicMock()
                mock_bedrock_class.return_value = mock_bedrock
                mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
                mock_bedrock.extract_tool_response.return_value = classification_result
                
                # Execute
                result = await main(f"s3://document-extraction-logistically/test/image.{format_ext}")
                
                # Verify processing
                assert result['classification_result'] == classification_result
                mock_textract.process_document_classification.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_pipeline_execution_info_accuracy(self, mock_settings):
        """Test that execution info accurately reflects pipeline execution."""
        s3_uri = "s3://document-extraction-logistically/test/document.pdf"
        combined_text = "<page1>\nTest document\n</page1>"
        classification_result = {"documents": [{"page_no": 1, "doc_type": "invoice"}]}
        
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup mocks
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (
                combined_text,
                [{"DocumentMetadata": {"Pages": 1}}]
            )
            
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
            mock_bedrock.extract_tool_response.return_value = classification_result
            
            # Execute
            result = await main(s3_uri)
            
            # Verify execution info accuracy
            exec_info = result['execution_info']
            
            # Textract execution info
            textract_info = exec_info['textract']
            assert textract_info['file_path'] == s3_uri
            assert textract_info['aws_region'] == mock_settings.AWS_REGION_EXTRACTION
            assert textract_info['bucket_name'] == mock_settings.S3_BUCKET_NAME
            
            # Bedrock execution info
            bedrock_info = exec_info['bedrock']
            assert bedrock_info['aws_region'] == mock_settings.AWS_REGION_CLASSIFICATION
            assert bedrock_info['model_id'] == mock_settings.MODEL_ID_CLASSIFICATION
            assert bedrock_info['user_prompt'] == combined_text
            assert bedrock_info['temperature'] == mock_settings.TEMPERATURE_CLASSIFICATION
            assert bedrock_info['max_tokens'] == mock_settings.MAX_TOKENS_CLASSIFICATION
            assert bedrock_info['reasoning_effort'] == mock_settings.REASONING_EFFORT_CLASSIFICATION
    
    @pytest.mark.asyncio
    async def test_pipeline_data_flow_integrity(self, mock_settings):
        """Test that data flows correctly through the pipeline without corruption."""
        # Setup test data with specific content to track
        original_combined_text = "<page1>\nSpecific test content for data integrity\nInvoice #TEST123\n</page1>"
        expected_classification = {"documents": [{"page_no": 1, "doc_type": "invoice"}]}
        
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup mocks to track data flow
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (
                original_combined_text,
                [{"DocumentMetadata": {"Pages": 1}, "test_marker": "textract_data"}]
            )
            
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {
                "output": {"message": {"content": []}},
                "test_marker": "bedrock_data"
            }
            mock_bedrock.extract_tool_response.return_value = expected_classification
            
            # Execute
            result = await main("s3://document-extraction-logistically/test/integrity_test.pdf")
            
            # Verify data integrity
            # Check that Bedrock received the exact text from Textract
            bedrock_call_args = mock_bedrock.call_bedrock_converse.call_args
            user_message = bedrock_call_args[1]['messages'][0]['content'][0]['text']
            assert user_message == original_combined_text
            
            # Check that original data is preserved in result
            assert result['classification_result'] == expected_classification
            assert result['textract_response_object'][0]['test_marker'] == "textract_data"
            assert result['bedrock_response_object']['test_marker'] == "bedrock_data"
            assert result['execution_info']['bedrock']['user_prompt'] == original_combined_text
    
    @pytest.mark.asyncio
    async def test_pipeline_with_s3_temp_bucket_usage(self, mock_settings, temp_s3_bucket, temp_s3_prefix):
        """Test that pipeline correctly uses temporary S3 bucket and prefix."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class:
            
            # Setup mocks
            mock_textract = MagicMock()
            mock_textract_class.return_value = mock_textract
            mock_textract.process_document_classification.return_value = (
                "<page1>\nTest content\n</page1>",
                [{"DocumentMetadata": {"Pages": 1}}]
            )
            
            mock_bedrock = MagicMock()
            mock_bedrock_class.return_value = mock_bedrock
            mock_bedrock.call_bedrock_converse.return_value = {"output": {"message": {"content": []}}}
            mock_bedrock.extract_tool_response.return_value = {"documents": [{"page_no": 1, "doc_type": "other"}]}
            
            # Execute
            await main("s3://document-extraction-logistically/test/document.pdf")
            
            # Verify that TextractProcessor was called with correct bucket and prefix
            textract_call_args = mock_textract.process_document_classification.call_args
            assert textract_call_args[0][1] == mock_settings.S3_BUCKET_NAME  # temp_bucket_name
            assert textract_call_args[0][2] == mock_settings.TEXTRACT_TEMP_PREFIX  # temp_prefix
