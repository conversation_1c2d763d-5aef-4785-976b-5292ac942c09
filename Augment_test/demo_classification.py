#!/usr/bin/env python3
"""
Demo script for testing classification.py with real documents.

This script demonstrates how to:
1. Upload a test document to S3
2. Run the classification process
3. Compare results with expected output
4. Clean up uploaded files
"""

import asyncio
import json
import sys
from pathlib import Path
from uuid import uuid4

# Add the project root to the path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from app.llm.classification import main
from app.services.s3_service import s3_service
from app.core.configuration import settings


async def upload_file_to_s3(file_path: Path, s3_prefix: str) -> str:
    """Upload a file to S3 and return the S3 URI."""
    bucket_name = settings.S3_BUCKET_NAME
    object_key = f"{s3_prefix}/{file_path.name}"
    
    # Read file content
    with open(file_path, 'rb') as f:
        file_content = f.read()
    
    # Determine content type
    content_types = {
        '.pdf': 'application/pdf',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png'
    }
    content_type = content_types.get(file_path.suffix.lower(), 'application/octet-stream')
    
    # Upload to S3
    success = await s3_service.upload_file_from_bytes(
        bucket_name=bucket_name,
        object_key=object_key,
        content_bytes=file_content,
        content_type=content_type
    )
    
    if not success:
        raise Exception(f"Failed to upload {file_path} to S3")
    
    return f"s3://{bucket_name}/{object_key}"


async def cleanup_s3_file(s3_uri: str):
    """Clean up uploaded file from S3."""
    try:
        if s3_uri.startswith("s3://"):
            parts = s3_uri[5:].split("/", 1)
            if len(parts) == 2:
                bucket_name, object_key = parts
                await s3_service.delete_file(bucket_name, object_key)
                print(f"✅ Cleaned up S3 file: {s3_uri}")
    except Exception as e:
        print(f"⚠️  Warning: Failed to cleanup S3 file {s3_uri}: {e}")


def load_expected_output(output_file: Path) -> dict:
    """Load expected output from JSON file."""
    with open(output_file, 'r') as f:
        return json.load(f)


def compare_results(actual: dict, expected: dict) -> dict:
    """Compare actual results with expected results."""
    comparison = {
        "match": False,
        "details": {}
    }
    
    # Check classification result
    if "classification_result" in actual and "classification_result" in expected:
        actual_docs = actual["classification_result"].get("documents", [])
        expected_docs = expected["classification_result"].get("documents", [])
        
        if len(actual_docs) == len(expected_docs):
            matches = []
            for actual_doc, expected_doc in zip(actual_docs, expected_docs):
                doc_match = (
                    actual_doc.get("page_no") == expected_doc.get("page_no") and
                    actual_doc.get("doc_type") == expected_doc.get("doc_type")
                )
                matches.append({
                    "match": doc_match,
                    "actual": actual_doc,
                    "expected": expected_doc
                })
            
            comparison["match"] = all(m["match"] for m in matches)
            comparison["details"]["document_matches"] = matches
        else:
            comparison["details"]["count_mismatch"] = {
                "actual": len(actual_docs),
                "expected": len(expected_docs)
            }
    
    return comparison


async def demo_single_document(document_name: str = "invoice"):
    """
    Demonstrate classification of a single document.
    
    Args:
        document_name: Name of the document to test (without extension)
    """
    print(f"🧪 Demo: Classifying '{document_name}' document")
    print("=" * 50)
    
    # Paths
    input_dir = project_root / "tests" / "classification_input_data"
    output_dir = project_root / "tests" / "classification_output_data"
    
    # Find input file (try different extensions)
    input_file = None
    for ext in [".pdf", ".jpg", ".jpeg", ".png"]:
        potential_file = input_dir / f"{document_name}{ext}"
        if potential_file.exists():
            input_file = potential_file
            break
    
    if not input_file:
        print(f"❌ Input file not found for '{document_name}'")
        return False
    
    # Find expected output file
    output_file = output_dir / f"{document_name}.json"
    if not output_file.exists():
        print(f"❌ Expected output file not found: {output_file}")
        return False
    
    print(f"📄 Input file: {input_file}")
    print(f"📋 Expected output: {output_file}")
    
    # Generate unique S3 prefix
    s3_prefix = f"demo-classification-{uuid4().hex[:8]}"
    s3_uri = None
    
    try:
        # Step 1: Upload to S3
        print(f"\n1️⃣ Uploading to S3...")
        s3_uri = await upload_file_to_s3(input_file, s3_prefix)
        print(f"✅ Uploaded to: {s3_uri}")
        
        # Step 2: Run classification
        print(f"\n2️⃣ Running classification...")
        result = await main(s3_uri)
        print(f"✅ Classification completed")
        
        # Step 3: Load expected results
        print(f"\n3️⃣ Loading expected results...")
        expected = load_expected_output(output_file)
        print(f"✅ Expected results loaded")
        
        # Step 4: Compare results
        print(f"\n4️⃣ Comparing results...")
        comparison = compare_results(result, expected)
        
        if comparison["match"]:
            print("✅ Results match expected output!")
        else:
            print("❌ Results don't match expected output")
            print("Details:", json.dumps(comparison["details"], indent=2))
        
        # Step 5: Display results
        print(f"\n📊 Classification Results:")
        classification = result["classification_result"]
        for i, doc in enumerate(classification["documents"]):
            print(f"   Page {doc['page_no']}: {doc['doc_type']}")
        
        # Display execution info
        exec_info = result["execution_info"]
        print(f"\n🔧 Execution Info:")
        print(f"   Textract Region: {exec_info['textract']['aws_region']}")
        print(f"   Bedrock Region: {exec_info['bedrock']['aws_region']}")
        print(f"   Model: {exec_info['bedrock']['model_id']}")
        
        return comparison["match"]
        
    except Exception as e:
        print(f"❌ Error during classification: {e}")
        return False
        
    finally:
        # Step 6: Cleanup
        if s3_uri:
            print(f"\n5️⃣ Cleaning up...")
            await cleanup_s3_file(s3_uri)


async def demo_multiple_documents():
    """Demonstrate classification of multiple documents."""
    print("🧪 Demo: Classifying multiple documents")
    print("=" * 50)
    
    # Test documents
    test_docs = ["invoice", "bol", "pod", "fuel_receipt"]
    results = []
    
    for doc_name in test_docs:
        print(f"\n📄 Testing {doc_name}...")
        success = await demo_single_document(doc_name)
        results.append({"document": doc_name, "success": success})
        
        if success:
            print(f"✅ {doc_name}: PASSED")
        else:
            print(f"❌ {doc_name}: FAILED")
    
    # Summary
    print(f"\n📊 Summary:")
    total = len(results)
    passed = sum(1 for r in results if r["success"])
    print(f"   Total: {total}")
    print(f"   Passed: {passed}")
    print(f"   Failed: {total - passed}")
    print(f"   Success Rate: {passed/total:.1%}")


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Demo classification with real documents")
    parser.add_argument(
        "document",
        nargs="?",
        default="invoice",
        help="Document name to test (default: invoice)"
    )
    parser.add_argument(
        "--multiple",
        action="store_true",
        help="Test multiple documents"
    )
    
    args = parser.parse_args()
    
    # Check environment
    if not settings.S3_BUCKET_NAME:
        print("❌ S3_BUCKET_NAME not configured")
        return 1
    
    if not settings.AWS_ACCESS_KEY_ID:
        print("❌ AWS credentials not configured")
        return 1
    
    print("🚀 Classification Demo")
    print(f"S3 Bucket: {settings.S3_BUCKET_NAME}")
    print(f"Bedrock Model: {settings.MODEL_ID_CLASSIFICATION}")
    print()
    
    # Run demo
    try:
        if args.multiple:
            success = asyncio.run(demo_multiple_documents())
        else:
            success = asyncio.run(demo_single_document(args.document))
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
        return 130
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
