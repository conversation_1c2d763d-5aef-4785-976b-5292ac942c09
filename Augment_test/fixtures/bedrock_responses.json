{"invoice_classification": {"output": {"message": {"role": "assistant", "content": [{"text": "I'll analyze this document to classify its type based on the content and keywords present. I can see this is an invoice document with invoice number, amount, and billing information."}, {"toolUse": {"toolUseId": "classify-invoice-001", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 1247, "outputTokens": 89, "totalTokens": 1336}}, "bol_classification": {"output": {"message": {"role": "assistant", "content": [{"text": "This document appears to be a Bill of Lading based on the header text and the presence of shipper, consignee, and B/L number information."}, {"toolUse": {"toolUseId": "classify-bol-001", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "bol"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 1156, "outputTokens": 76, "totalTokens": 1232}}, "pod_classification": {"output": {"message": {"role": "assistant", "content": [{"text": "This is clearly a Proof of Delivery document, as indicated by the header and the presence of delivery date, recipient information, and signature confirmation."}, {"toolUse": {"toolUseId": "classify-pod-001", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "pod"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 1089, "outputTokens": 68, "totalTokens": 1157}}, "fuel_receipt_classification": {"output": {"message": {"role": "assistant", "content": [{"text": "This document is a fuel receipt from a gas station, containing fuel purchase details including gallons, fuel type, and total amount."}, {"toolUse": {"toolUseId": "classify-fuel-001", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 1198, "outputTokens": 72, "totalTokens": 1270}}, "multi_page_mixed_classification": {"output": {"message": {"role": "assistant", "content": [{"text": "Analyzing this multi-page document, I can identify different document types on each page: an invoice on page 1, a proof of delivery on page 2, and a bill of lading on page 3."}, {"toolUse": {"toolUseId": "classify-multi-001", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "invoice"}, {"page_no": 2, "doc_type": "pod"}, {"page_no": 3, "doc_type": "bol"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2156, "outputTokens": 145, "totalTokens": 2301}}, "scale_ticket_classification": {"output": {"message": {"role": "assistant", "content": [{"text": "This document appears to be a scale ticket based on weight measurements and scale information present in the content."}, {"toolUse": {"toolUseId": "classify-scale-001", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 1134, "outputTokens": 65, "totalTokens": 1199}}, "lumper_receipt_classification": {"output": {"message": {"role": "assistant", "content": [{"text": "This document is a lumper receipt for loading/unloading services, containing service details and payment information."}, {"toolUse": {"toolUseId": "classify-lumper-001", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 1167, "outputTokens": 71, "totalTokens": 1238}}, "other_classification": {"output": {"message": {"role": "assistant", "content": [{"text": "This document doesn't clearly match any of the specific logistics document types in our classification system, so I'll classify it as 'other'."}, {"toolUse": {"toolUseId": "classify-other-001", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "other"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 1045, "outputTokens": 78, "totalTokens": 1123}}, "error_max_tokens": {"output": {"message": {"role": "assistant", "content": [{"text": "I'm analyzing this document to classify its type, but the response was truncated due to token limits. Please increase the MAX_TOKENS_CLASSIFICATION setting."}]}}, "stopReason": "max_tokens", "usage": {"inputTokens": 2500, "outputTokens": 5000, "totalTokens": 7500}}, "error_content_filter": {"output": {"message": {"role": "assistant", "content": [{"text": "I cannot process this document due to content filtering restrictions."}]}}, "stopReason": "content_filtered", "usage": {"inputTokens": 1200, "outputTokens": 25, "totalTokens": 1225}}}