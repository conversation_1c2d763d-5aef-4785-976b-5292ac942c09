# Classification Tests

Comprehensive test suite for the `app.llm.classification` module.

## Overview

This test suite provides comprehensive coverage for the document classification pipeline, including:

- **Unit Tests**: Individual function testing with mocked dependencies
- **Integration Tests**: Full pipeline testing with mocked AWS services
- **Error Handling Tests**: Edge cases, malformed inputs, and service failures
- **End-to-End Tests**: Complete pipeline validation with realistic scenarios

## Test Structure

```
Augment_test/
├── conftest.py                     # Shared pytest fixtures
├── test_classification_unit.py     # Unit tests for core functions
├── test_classification_integration.py  # Integration tests
├── test_classification_errors.py   # Error handling and edge cases
├── test_classification_e2e.py      # End-to-end pipeline tests
├── fixtures/                       # Test data fixtures
│   ├── textract_responses.json     # Sample Textract responses
│   ├── bedrock_responses.json      # Sample Bedrock responses
│   └── combined_texts.json         # Sample combined text data
├── run_tests.py                    # Test runner script
└── README_TESTS.md                 # This file
```

## Quick Start

### Install Dependencies

```bash
# Install test dependencies
python Augment_test/run_tests.py --install-deps

# Or manually install
pip install pytest>=7.0.0 pytest-asyncio>=0.21.0 pytest-cov>=4.0.0 pytest-mock>=3.10.0
```

### Run Tests

```bash
# Run all tests
python Augment_test/run_tests.py

# Run specific test types
python Augment_test/run_tests.py unit
python Augment_test/run_tests.py integration
python Augment_test/run_tests.py e2e
python Augment_test/run_tests.py errors

# Run with verbose output
python Augment_test/run_tests.py -v

# Run with coverage reporting
python Augment_test/run_tests.py -c
```

### Direct pytest Usage

```bash
# Run all classification tests
pytest Augment_test/test_classification_*.py -v

# Run specific test file
pytest Augment_test/test_classification_unit.py -v

# Run with coverage
pytest Augment_test/test_classification_*.py --cov=app.llm.classification --cov-report=html
```

## Test Categories

### Unit Tests (`test_classification_unit.py`)

Tests individual functions in isolation:

- `setup_logging()` function
- `get_document_types_with_bedrock()` function  
- `main()` function with mocked dependencies

**Key Features:**
- Mocked AWS services
- Input validation testing
- Return value verification
- Error propagation testing

### Integration Tests (`test_classification_integration.py`)

Tests the complete pipeline with mocked services:

- Full pipeline data flow
- Multi-page document processing
- Different document type handling
- Execution info accuracy
- Data integrity validation

**Key Features:**
- Real data flow between components
- Mocked AWS Textract and Bedrock
- Pipeline execution order validation
- Result structure verification

### Error Handling Tests (`test_classification_errors.py`)

Tests error scenarios and edge cases:

- Input validation errors
- Textract service failures
- Bedrock service failures
- Malformed responses
- Timeout handling
- Large document processing

**Key Features:**
- Comprehensive error coverage
- Edge case validation
- Service failure simulation
- Boundary condition testing

### End-to-End Tests (`test_classification_e2e.py`)

Complete pipeline validation with realistic scenarios:

- Invoice classification pipeline
- Multi-page mixed document processing
- Image document classification
- Schema validation
- Real-world data scenarios

**Key Features:**
- Realistic test data
- Complete pipeline validation
- Schema compliance testing
- Production-like scenarios

## Test Data and Fixtures

### Shared Fixtures (`conftest.py`)

- `mock_settings`: Mocked configuration settings
- `mock_textract_processor`: Mocked TextractProcessor
- `mock_bedrock_processor`: Mocked BedrockProcessor
- `sample_s3_uris`: Test S3 URIs
- `classification_test_data`: Loaded test data
- `error_scenarios`: Common error scenarios

### Test Data Files

- **`textract_responses.json`**: Sample Textract API responses for different document types
- **`bedrock_responses.json`**: Sample Bedrock API responses with tool calls
- **`combined_texts.json`**: Sample combined text from Textract processing

## Mocking Strategy

The test suite uses comprehensive mocking to:

1. **Isolate Components**: Test classification logic without AWS dependencies
2. **Control Responses**: Use predictable test data for consistent results
3. **Simulate Errors**: Test error handling without actual service failures
4. **Speed Up Tests**: Avoid network calls and service delays

### AWS Service Mocking

- **Textract**: Mocked `TextractProcessor.process_document_classification()`
- **Bedrock**: Mocked `BedrockProcessor.call_bedrock_converse()` and `extract_tool_response()`
- **S3**: Mocked through Textract processor (no direct S3 calls in classification.py)

## Coverage Goals

The test suite aims for:

- **Function Coverage**: 100% of functions in `classification.py`
- **Line Coverage**: >95% of executable lines
- **Branch Coverage**: >90% of conditional branches
- **Error Path Coverage**: All error scenarios and edge cases

## Test Execution Environment

### Requirements

- Python 3.8+
- pytest 7.0+
- pytest-asyncio 0.21+
- pytest-cov 4.0+ (for coverage)
- pytest-mock 3.10+ (for enhanced mocking)

### Environment Variables

Tests use mocked settings, but you can set these for integration testing:

```bash
export AWS_REGION_CLASSIFICATION=us-west-2
export AWS_REGION_EXTRACTION=us-east-1
export S3_BUCKET_NAME=document-extraction-logistically
export MODEL_ID_CLASSIFICATION=openai.gpt-oss-20b-1:0
```

## Continuous Integration

For CI/CD pipelines:

```bash
# Install dependencies
pip install -r requirements.txt
pip install pytest pytest-asyncio pytest-cov pytest-mock

# Run tests with coverage
pytest Augment_test/test_classification_*.py \
    --cov=app.llm.classification \
    --cov-report=xml \
    --cov-report=term-missing \
    --junitxml=test-results.xml

# Check coverage threshold (optional)
coverage report --fail-under=95
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure project root is in Python path
2. **Async Test Failures**: Install `pytest-asyncio` plugin
3. **Mock Failures**: Check that patches target the correct module paths
4. **Fixture Errors**: Verify `conftest.py` is in the test directory

### Debug Mode

Run tests with maximum verbosity:

```bash
pytest Augment_test/test_classification_*.py -vvv --tb=long --capture=no
```

### Test Data Issues

If test data files are missing or corrupted:

1. Check that `fixtures/` directory exists
2. Verify JSON files are valid
3. Regenerate fixtures if needed

## Contributing

When adding new tests:

1. Follow existing naming conventions
2. Use appropriate fixtures from `conftest.py`
3. Add new fixtures for reusable test data
4. Update this README for significant changes
5. Ensure tests are deterministic and isolated

## Performance

Test execution times (approximate):

- Unit Tests: ~5-10 seconds
- Integration Tests: ~10-15 seconds  
- Error Tests: ~15-20 seconds
- E2E Tests: ~20-30 seconds
- **Total Suite**: ~50-75 seconds

For faster development cycles, run specific test categories or use pytest's `-k` option to run subsets of tests.
