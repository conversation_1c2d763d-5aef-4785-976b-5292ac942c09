"""
Real integration tests for app.llm.classification module.

This module contains integration tests that actually run the classification process
on real test documents from tests/classification_input_data and compare the output
with expected results from tests/classification_output_data.
"""

import pytest
import asyncio
import json
import os
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List
import sys
from uuid import uuid4

# Add the project root to the path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from app.llm.classification import main as classify_document
from app.services.s3_service import s3_service
from app.core.configuration import settings


class TestRealClassificationIntegration:
    """Real integration tests using actual AWS services and test documents."""
    
    @pytest.fixture(scope="class")
    def test_data_paths(self):
        """Get paths to test input and output data."""
        input_dir = project_root / "tests" / "classification_input_data"
        output_dir = project_root / "tests" / "classification_output_data"
        return {
            "input_dir": input_dir,
            "output_dir": output_dir
        }
    
    @pytest.fixture(scope="class")
    def test_documents(self, test_data_paths):
        """Get list of test documents with their expected outputs."""
        input_dir = test_data_paths["input_dir"]
        output_dir = test_data_paths["output_dir"]
        
        documents = []
        
        # Get all input files
        for input_file in input_dir.glob("*"):
            if input_file.is_file():
                # Find corresponding output file
                base_name = input_file.stem
                output_file = output_dir / f"{base_name}.json"
                
                if output_file.exists():
                    documents.append({
                        "name": base_name,
                        "input_file": input_file,
                        "output_file": output_file,
                        "extension": input_file.suffix.lower()
                    })
        
        return documents
    
    @pytest.fixture(scope="class")
    def s3_test_prefix(self):
        """Generate unique S3 prefix for test files."""
        return f"test-classification-{uuid4().hex[:8]}"
    
    async def upload_test_file_to_s3(self, file_path: Path, s3_prefix: str) -> str:
        """
        Upload a test file to S3 and return the S3 URI.
        
        Args:
            file_path: Local path to the test file
            s3_prefix: S3 prefix for organizing test files
            
        Returns:
            S3 URI of the uploaded file
        """
        bucket_name = settings.S3_BUCKET_NAME
        object_key = f"{s3_prefix}/{file_path.name}"
        
        # Read file content
        with open(file_path, 'rb') as f:
            file_content = f.read()
        
        # Upload to S3
        success = await s3_service.upload_file_from_bytes(
            bucket_name=bucket_name,
            object_key=object_key,
            content_bytes=file_content,
            content_type=self._get_content_type(file_path.suffix)
        )
        
        if not success:
            raise Exception(f"Failed to upload {file_path} to S3")
        
        return f"s3://{bucket_name}/{object_key}"
    
    def _get_content_type(self, file_extension: str) -> str:
        """Get content type based on file extension."""
        content_types = {
            '.pdf': 'application/pdf',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.tiff': 'image/tiff',
            '.tif': 'image/tiff'
        }
        return content_types.get(file_extension.lower(), 'application/octet-stream')
    
    def load_expected_output(self, output_file: Path) -> Dict[str, Any]:
        """Load expected output from JSON file."""
        with open(output_file, 'r') as f:
            return json.load(f)
    
    def compare_classification_results(self, actual: Dict[str, Any], expected: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compare actual classification results with expected results.
        
        Returns:
            Dictionary with comparison results and details
        """
        comparison = {
            "classification_match": False,
            "structure_match": False,
            "details": {}
        }
        
        # Check if both have classification_result
        if "classification_result" not in actual or "classification_result" not in expected:
            comparison["details"]["missing_classification_result"] = True
            return comparison
        
        actual_classification = actual["classification_result"]
        expected_classification = expected["classification_result"]
        
        # Check structure
        if "documents" in actual_classification and "documents" in expected_classification:
            comparison["structure_match"] = True
            
            actual_docs = actual_classification["documents"]
            expected_docs = expected_classification["documents"]
            
            # Compare number of documents
            if len(actual_docs) == len(expected_docs):
                comparison["details"]["document_count_match"] = True
                
                # Compare each document
                doc_matches = []
                for i, (actual_doc, expected_doc) in enumerate(zip(actual_docs, expected_docs)):
                    doc_match = {
                        "page_no_match": actual_doc.get("page_no") == expected_doc.get("page_no"),
                        "doc_type_match": actual_doc.get("doc_type") == expected_doc.get("doc_type"),
                        "actual_doc_type": actual_doc.get("doc_type"),
                        "expected_doc_type": expected_doc.get("doc_type")
                    }
                    doc_matches.append(doc_match)
                
                comparison["details"]["document_matches"] = doc_matches
                
                # Overall classification match
                all_docs_match = all(
                    doc["page_no_match"] and doc["doc_type_match"] 
                    for doc in doc_matches
                )
                comparison["classification_match"] = all_docs_match
            else:
                comparison["details"]["document_count_mismatch"] = {
                    "actual": len(actual_docs),
                    "expected": len(expected_docs)
                }
        
        return comparison
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_single_document_classification(self, test_documents, s3_test_prefix):
        """Test classification of a single document (invoice) to validate the setup."""
        # Find invoice test document
        invoice_doc = next((doc for doc in test_documents if doc["name"] == "invoice"), None)
        
        if not invoice_doc:
            pytest.skip("Invoice test document not found")
        
        # Upload to S3
        s3_uri = await self.upload_test_file_to_s3(invoice_doc["input_file"], s3_test_prefix)
        
        try:
            # Run classification
            result = await classify_document(s3_uri)
            
            # Load expected output
            expected = self.load_expected_output(invoice_doc["output_file"])
            
            # Compare results
            comparison = self.compare_classification_results(result, expected)
            
            # Assertions
            assert comparison["structure_match"], "Result structure doesn't match expected format"
            assert comparison["classification_match"], f"Classification mismatch: {comparison['details']}"
            
            # Verify specific invoice classification
            actual_docs = result["classification_result"]["documents"]
            assert len(actual_docs) == 1, f"Expected 1 document, got {len(actual_docs)}"
            assert actual_docs[0]["doc_type"] == "invoice", f"Expected 'invoice', got '{actual_docs[0]['doc_type']}'"
            
        finally:
            # Cleanup: Delete uploaded file from S3
            await self._cleanup_s3_file(s3_uri)
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    @pytest.mark.parametrize("doc_type", [
        "bol", "pod", "scale_ticket", "fuel_receipt", "lumper_receipt"
    ])
    async def test_specific_document_types(self, test_documents, s3_test_prefix, doc_type):
        """Test classification of specific document types."""
        # Find the specific document
        test_doc = next((doc for doc in test_documents if doc["name"] == doc_type), None)
        
        if not test_doc:
            pytest.skip(f"{doc_type} test document not found")
        
        # Upload to S3
        s3_uri = await self.upload_test_file_to_s3(test_doc["input_file"], s3_test_prefix)
        
        try:
            # Run classification
            result = await main(s3_uri)
            
            # Load expected output
            expected = self.load_expected_output(test_doc["output_file"])
            
            # Compare results
            comparison = self.compare_classification_results(result, expected)
            
            # Assertions
            assert comparison["structure_match"], f"Result structure doesn't match for {doc_type}"
            assert comparison["classification_match"], f"Classification mismatch for {doc_type}: {comparison['details']}"
            
            # Verify specific document type classification
            actual_docs = result["classification_result"]["documents"]
            assert len(actual_docs) >= 1, f"Expected at least 1 document for {doc_type}, got {len(actual_docs)}"
            
            # Check if the expected doc_type appears in the results
            actual_doc_types = [doc["doc_type"] for doc in actual_docs]
            assert doc_type in actual_doc_types, f"Expected '{doc_type}' in {actual_doc_types}"
            
        finally:
            # Cleanup
            await self._cleanup_s3_file(s3_uri)
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_image_document_classification(self, test_documents, s3_test_prefix):
        """Test classification of image documents (JPEG, PNG)."""
        # Find image documents
        image_docs = [doc for doc in test_documents if doc["extension"] in [".jpg", ".jpeg", ".png"]]
        
        if not image_docs:
            pytest.skip("No image test documents found")
        
        for image_doc in image_docs[:2]:  # Test first 2 image documents
            # Upload to S3
            s3_uri = await self.upload_test_file_to_s3(image_doc["input_file"], s3_test_prefix)
            
            try:
                # Run classification
                result = await main(s3_uri)
                
                # Load expected output
                expected = self.load_expected_output(image_doc["output_file"])
                
                # Compare results
                comparison = self.compare_classification_results(result, expected)
                
                # Assertions
                assert comparison["structure_match"], f"Result structure doesn't match for {image_doc['name']}"
                
                # For images, we're more lenient on exact matches due to OCR variability
                actual_docs = result["classification_result"]["documents"]
                assert len(actual_docs) >= 1, f"Expected at least 1 document for {image_doc['name']}"
                
                # Verify that we got a valid document type
                valid_doc_types = [
                    "invoice", "comm_invoice", "lumper_receipt", "bol", "pod", 
                    "rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", 
                    "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", 
                    "combined_carrier_documents", "customs_doc", "other", "so_confirmation", 
                    "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"
                ]
                
                for doc in actual_docs:
                    assert doc["doc_type"] in valid_doc_types, f"Invalid doc_type: {doc['doc_type']}"
                
            finally:
                # Cleanup
                await self._cleanup_s3_file(s3_uri)
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_batch_classification_accuracy(self, test_documents, s3_test_prefix):
        """Test classification accuracy across multiple documents."""
        # Test a subset of documents to avoid long test times
        test_subset = test_documents[:5]  # Test first 5 documents
        
        results = []
        
        for test_doc in test_subset:
            # Upload to S3
            s3_uri = await self.upload_test_file_to_s3(test_doc["input_file"], s3_test_prefix)
            
            try:
                # Run classification
                result = await main(s3_uri)
                
                # Load expected output
                expected = self.load_expected_output(test_doc["output_file"])
                
                # Compare results
                comparison = self.compare_classification_results(result, expected)
                
                results.append({
                    "document": test_doc["name"],
                    "classification_match": comparison["classification_match"],
                    "structure_match": comparison["structure_match"],
                    "details": comparison["details"]
                })
                
            finally:
                # Cleanup
                await self._cleanup_s3_file(s3_uri)
        
        # Calculate accuracy
        total_docs = len(results)
        structure_matches = sum(1 for r in results if r["structure_match"])
        classification_matches = sum(1 for r in results if r["classification_match"])
        
        structure_accuracy = structure_matches / total_docs
        classification_accuracy = classification_matches / total_docs
        
        # Assertions
        assert structure_accuracy >= 0.8, f"Structure accuracy too low: {structure_accuracy:.2%}"
        assert classification_accuracy >= 0.6, f"Classification accuracy too low: {classification_accuracy:.2%}"
        
        # Log results for analysis
        print(f"\nBatch Classification Results:")
        print(f"Total documents tested: {total_docs}")
        print(f"Structure accuracy: {structure_accuracy:.2%}")
        print(f"Classification accuracy: {classification_accuracy:.2%}")
        
        for result in results:
            status = "✓" if result["classification_match"] else "✗"
            print(f"{status} {result['document']}: {result['classification_match']}")
    
    async def _cleanup_s3_file(self, s3_uri: str):
        """Clean up uploaded test file from S3."""
        try:
            # Parse S3 URI
            if s3_uri.startswith("s3://"):
                parts = s3_uri[5:].split("/", 1)
                if len(parts) == 2:
                    bucket_name, object_key = parts
                    await s3_service.delete_file(bucket_name, object_key)
        except Exception as e:
            print(f"Warning: Failed to cleanup S3 file {s3_uri}: {e}")
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_classification_result_structure_validation(self, test_documents, s3_test_prefix):
        """Test that all classification results have the expected structure."""
        # Test one document to validate structure
        test_doc = test_documents[0] if test_documents else None
        
        if not test_doc:
            pytest.skip("No test documents found")
        
        # Upload to S3
        s3_uri = await self.upload_test_file_to_s3(test_doc["input_file"], s3_test_prefix)
        
        try:
            # Run classification
            result = await main(s3_uri)
            
            # Validate top-level structure
            required_keys = ["classification_result", "textract_response_object", 
                           "bedrock_response_object", "execution_info"]
            for key in required_keys:
                assert key in result, f"Missing required key: {key}"
            
            # Validate classification_result structure
            classification = result["classification_result"]
            assert "documents" in classification, "Missing 'documents' in classification_result"
            assert isinstance(classification["documents"], list), "'documents' should be a list"
            
            # Validate document structure
            for doc in classification["documents"]:
                assert "page_no" in doc, "Missing 'page_no' in document"
                assert "doc_type" in doc, "Missing 'doc_type' in document"
                assert isinstance(doc["page_no"], int), "'page_no' should be an integer"
                assert isinstance(doc["doc_type"], str), "'doc_type' should be a string"
                assert doc["page_no"] > 0, "'page_no' should be positive"
            
            # Validate execution_info structure
            exec_info = result["execution_info"]
            assert "textract" in exec_info, "Missing 'textract' in execution_info"
            assert "bedrock" in exec_info, "Missing 'bedrock' in execution_info"
            
            # Validate textract execution info
            textract_info = exec_info["textract"]
            textract_required = ["aws_region", "file_path", "bucket_name"]
            for key in textract_required:
                assert key in textract_info, f"Missing '{key}' in textract execution_info"
            
            # Validate bedrock execution info
            bedrock_info = exec_info["bedrock"]
            bedrock_required = ["aws_region", "model_id", "user_prompt", "temperature", "max_tokens"]
            for key in bedrock_required:
                assert key in bedrock_info, f"Missing '{key}' in bedrock execution_info"
            
        finally:
            # Cleanup
            await self._cleanup_s3_file(s3_uri)
