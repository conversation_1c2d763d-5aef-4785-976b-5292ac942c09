"""
Shared pytest fixtures for classification.py tests.

This module provides common fixtures for testing the document classification
functionality, including mocked AWS services, test data, and configuration.
"""

import json
import pytest
from unittest.mock import Mock, MagicMock, patch
from typing import Dict, Any, List
import tempfile
import os
from pathlib import Path

# Test data directory paths
TEST_INPUT_DIR = Path(__file__).parent.parent / "tests" / "classification_input_data"
TEST_OUTPUT_DIR = Path(__file__).parent.parent / "tests" / "classification_output_data"


@pytest.fixture
def mock_settings():
    """Mock settings configuration for tests."""
    with patch('app.llm.classification.settings') as mock_settings:
        # AWS Configuration
        mock_settings.AWS_ACCESS_KEY_ID = "test_access_key"
        mock_settings.AWS_SECRET_ACCESS_KEY = "test_secret_key"
        mock_settings.AWS_SESSION_TOKEN = "test_session_token"
        mock_settings.AWS_REGION_CLASSIFICATION = "us-west-2"
        mock_settings.AWS_REGION_EXTRACTION = "us-east-1"
        
        # S3 Configuration
        mock_settings.S3_BUCKET_NAME = "document-extraction-logistically"
        mock_settings.TEXTRACT_TEMP_PREFIX = "temp-document-processing"
        
        # Bedrock Configuration
        mock_settings.MODEL_ID_CLASSIFICATION = "openai.gpt-oss-20b-1:0"
        mock_settings.TEMPERATURE_CLASSIFICATION = 0.7
        mock_settings.MAX_TOKENS_CLASSIFICATION = 5000
        mock_settings.REASONING_EFFORT_CLASSIFICATION = "medium"
        
        # Textract Configuration
        mock_settings.TEXTRACT_MAX_WORKERS = 6
        mock_settings.TEXTRACT_POLL_INTERVAL = 5
        mock_settings.TEXTRACT_MAX_WAIT_TIME = 300
        mock_settings.TEXTRACT_MAX_FILE_SIZE_MB = 10
        mock_settings.TEXTRACT_MEMORY_LIMIT_MB = 512
        mock_settings.TEXTRACT_S3_CLEANUP_RETRY_ATTEMPTS = 3
        mock_settings.TEXTRACT_S3_CLEANUP_RETRY_DELAY = 3
        
        # Sample file for testing
        mock_settings.S3_SAMPLE_FILE_CLASSIFICATION = "s3://document-extraction-logistically/sample/test_document.pdf"
        
        yield mock_settings


@pytest.fixture
def mock_textract_processor():
    """Mock TextractProcessor for testing."""
    with patch('app.llm.classification.TextractProcessor') as mock_class:
        mock_instance = MagicMock()
        mock_class.return_value = mock_instance
        
        # Mock the process_document_classification method
        mock_instance.process_document_classification.return_value = (
            "<page1>\nSample invoice text\nInvoice #12345\nAmount: $1,000.00\n</page1>",
            [sample_textract_response()]
        )
        
        yield mock_instance


@pytest.fixture
def mock_bedrock_processor():
    """Mock BedrockProcessor for testing."""
    with patch('app.llm.classification.BedrockProcessor') as mock_class:
        mock_instance = MagicMock()
        mock_class.return_value = mock_instance
        
        # Mock the call_bedrock_converse method
        mock_instance.call_bedrock_converse.return_value = sample_bedrock_response()
        
        # Mock the extract_tool_response method
        mock_instance.extract_tool_response.return_value = {
            "documents": [
                {
                    "page_no": 1,
                    "doc_type": "invoice"
                }
            ]
        }
        
        yield mock_instance


@pytest.fixture
def sample_s3_uris():
    """Sample S3 URIs for testing."""
    return {
        "pdf": "s3://document-extraction-logistically/test/sample.pdf",
        "jpg": "s3://document-extraction-logistically/test/sample.jpg",
        "png": "s3://document-extraction-logistically/test/sample.png",
        "invalid": "invalid://not-s3/file.pdf",
        "empty": "",
        "none": None
    }


@pytest.fixture
def sample_classification_result():
    """Sample classification result for testing."""
    return {
        "documents": [
            {
                "page_no": 1,
                "doc_type": "invoice"
            }
        ]
    }


@pytest.fixture
def sample_multi_page_classification_result():
    """Sample multi-page classification result for testing."""
    return {
        "documents": [
            {
                "page_no": 1,
                "doc_type": "invoice"
            },
            {
                "page_no": 2,
                "doc_type": "pod"
            },
            {
                "page_no": 3,
                "doc_type": "bol"
            }
        ]
    }


def sample_textract_response():
    """Generate a sample Textract response for testing."""
    return {
        "DocumentMetadata": {
            "Pages": 1
        },
        "Blocks": [
            {
                "BlockType": "PAGE",
                "Geometry": {
                    "BoundingBox": {
                        "Width": 1.0,
                        "Height": 1.0,
                        "Left": 0.0,
                        "Top": 0.0
                    }
                },
                "Id": "page-1",
                "Relationships": [
                    {
                        "Type": "CHILD",
                        "Ids": ["line-1", "line-2"]
                    }
                ]
            },
            {
                "BlockType": "LINE",
                "Confidence": 99.5,
                "Text": "Invoice #12345",
                "Geometry": {
                    "BoundingBox": {
                        "Width": 0.2,
                        "Height": 0.05,
                        "Left": 0.1,
                        "Top": 0.1
                    }
                },
                "Id": "line-1"
            },
            {
                "BlockType": "LINE",
                "Confidence": 98.7,
                "Text": "Amount: $1,000.00",
                "Geometry": {
                    "BoundingBox": {
                        "Width": 0.25,
                        "Height": 0.05,
                        "Left": 0.1,
                        "Top": 0.2
                    }
                },
                "Id": "line-2"
            }
        ]
    }


def sample_bedrock_response():
    """Generate a sample Bedrock response for testing."""
    return {
        "output": {
            "message": {
                "role": "assistant",
                "content": [
                    {
                        "text": "I'll analyze this document and classify it."
                    },
                    {
                        "toolUse": {
                            "toolUseId": "tool-1",
                            "name": "classify_logistics_doc_type",
                            "input": {
                                "documents": [
                                    {
                                        "page_no": 1,
                                        "doc_type": "invoice"
                                    }
                                ]
                            }
                        }
                    }
                ]
            }
        },
        "stopReason": "tool_use",
        "usage": {
            "inputTokens": 1500,
            "outputTokens": 150,
            "totalTokens": 1650
        }
    }


@pytest.fixture
def sample_combined_text():
    """Sample combined text from Textract for testing."""
    return "<page1>\nInvoice #12345\nNIMBLE TRANSPORT\nInvoice Amount: $1,300.00\nBill To: Superior Transport & Logistics\n</page1>"


@pytest.fixture
def sample_multi_page_combined_text():
    """Sample multi-page combined text for testing."""
    return """<page1>
Invoice #12345
NIMBLE TRANSPORT
Invoice Amount: $1,300.00
Bill To: Superior Transport & Logistics
</page1>

<page2>
Proof of Delivery
Delivered to: John Smith
Date: 2023-12-15
Signature: [Signed]
</page2>

<page3>
Bill of Lading
Shipper: ABC Company
Consignee: XYZ Corp
Cargo: Electronics
</page3>"""


@pytest.fixture
def temp_s3_bucket():
    """Temporary S3 bucket name for testing."""
    return "test-bucket-classification"


@pytest.fixture
def temp_s3_prefix():
    """Temporary S3 prefix for testing."""
    return "temp-test-processing"


@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    with patch('app.llm.classification.logging') as mock_logging:
        mock_logger = MagicMock()
        mock_logging.getLogger.return_value = mock_logger
        mock_logging.basicConfig = MagicMock()
        mock_logging.INFO = 20
        yield mock_logger


@pytest.fixture
def classification_test_data():
    """Load test data from the classification test directories."""
    test_data = {}
    
    # Load expected output data
    if TEST_OUTPUT_DIR.exists():
        for json_file in TEST_OUTPUT_DIR.glob("*.json"):
            doc_type = json_file.stem
            try:
                with open(json_file, 'r') as f:
                    test_data[doc_type] = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                # Create minimal test data if file doesn't exist or is invalid
                test_data[doc_type] = {
                    "classification_result": {
                        "documents": [{"page_no": 1, "doc_type": doc_type}]
                    },
                    "textract_response_object": [sample_textract_response()],
                    "bedrock_response_object": sample_bedrock_response()
                }
    
    return test_data


@pytest.fixture
def error_scenarios():
    """Common error scenarios for testing."""
    return {
        "textract_error": Exception("Textract service unavailable"),
        "bedrock_error": Exception("Bedrock model not found"),
        "s3_error": Exception("S3 bucket not accessible"),
        "invalid_response": {"invalid": "response"},
        "empty_response": {},
        "malformed_json": "not a json response",
        "timeout_error": TimeoutError("Request timed out"),
        "permission_error": PermissionError("Access denied")
    }
