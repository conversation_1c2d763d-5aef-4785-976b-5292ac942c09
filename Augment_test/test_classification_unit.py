"""
Unit tests for app.llm.classification module.

This module contains unit tests for individual functions in the classification
module, focusing on testing each function in isolation with mocked dependencies.
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, MagicMock, patch, AsyncMock
import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from app.llm.classification import setup_logging, get_document_types_with_bedrock, main


class TestSetupLogging:
    """Test cases for the setup_logging function."""
    
    def test_setup_logging_returns_logger(self, mock_logger):
        """Test that setup_logging returns a logger instance."""
        with patch('app.llm.classification.logging') as mock_logging:
            mock_logging.getLogger.return_value = mock_logger
            mock_logging.INFO = 20
            
            logger = setup_logging()
            
            assert logger is not None
            mock_logging.basicConfig.assert_called_once()
            mock_logging.getLogger.assert_called_once_with('app.llm.classification')
    
    def test_setup_logging_configures_correctly(self, mock_logger):
        """Test that setup_logging configures logging correctly."""
        with patch('app.llm.classification.logging') as mock_logging:
            mock_logging.getLogger.return_value = mock_logger
            mock_logging.INFO = 20
            
            logger = setup_logging()
            
            # Verify basicConfig was called with correct parameters
            mock_logging.basicConfig.assert_called_once_with(
                level=20,  # logging.INFO
                format='%(asctime)s - %(levelname)s - %(message)s'
            )
            
            # Verify logger level was set
            mock_logger.setLevel.assert_called_once_with(20)


class TestGetDocumentTypesWithBedrock:
    """Test cases for the get_document_types_with_bedrock function."""
    
    @pytest.mark.asyncio
    async def test_successful_classification(self, mock_settings, mock_bedrock_processor, 
                                           sample_combined_text, sample_classification_result):
        """Test successful document classification with Bedrock."""
        # Setup
        mock_bedrock_processor.call_bedrock_converse.return_value = {
            "output": {"message": {"content": [{"toolUse": {"input": sample_classification_result}}]}},
            "stopReason": "tool_use"
        }
        mock_bedrock_processor.extract_tool_response.return_value = sample_classification_result
        
        # Execute
        result, response = await get_document_types_with_bedrock(
            mock_bedrock_processor, sample_combined_text
        )
        
        # Verify
        assert result == sample_classification_result
        assert response is not None
        mock_bedrock_processor.call_bedrock_converse.assert_called_once()
        mock_bedrock_processor.extract_tool_response.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_bedrock_call_with_correct_parameters(self, mock_settings, mock_bedrock_processor, 
                                                       sample_combined_text):
        """Test that Bedrock is called with correct parameters."""
        # Execute
        await get_document_types_with_bedrock(mock_bedrock_processor, sample_combined_text)
        
        # Verify call parameters
        call_args = mock_bedrock_processor.call_bedrock_converse.call_args
        assert call_args[1]['model_id'] == mock_settings.MODEL_ID_CLASSIFICATION
        assert call_args[1]['messages'][0]['role'] == 'user'
        assert call_args[1]['messages'][0]['content'][0]['text'] == sample_combined_text
        assert call_args[1]['inference_config']['temperature'] == mock_settings.TEMPERATURE_CLASSIFICATION
        assert call_args[1]['inference_config']['maxTokens'] == mock_settings.MAX_TOKENS_CLASSIFICATION
    
    @pytest.mark.asyncio
    async def test_bedrock_service_error(self, mock_settings, mock_bedrock_processor, 
                                        sample_combined_text, error_scenarios):
        """Test handling of Bedrock service errors."""
        # Setup
        mock_bedrock_processor.call_bedrock_converse.side_effect = error_scenarios['bedrock_error']
        
        # Execute and verify
        with pytest.raises(Exception, match="Bedrock model not found"):
            await get_document_types_with_bedrock(mock_bedrock_processor, sample_combined_text)
    
    @pytest.mark.asyncio
    async def test_empty_combined_text(self, mock_settings, mock_bedrock_processor):
        """Test handling of empty combined text."""
        # Execute
        result, response = await get_document_types_with_bedrock(mock_bedrock_processor, "")
        
        # Verify that the function still processes empty text
        mock_bedrock_processor.call_bedrock_converse.assert_called_once()
        assert result is not None
    
    @pytest.mark.asyncio
    async def test_malformed_response_handling(self, mock_settings, mock_bedrock_processor, 
                                              sample_combined_text, error_scenarios):
        """Test handling of malformed Bedrock responses."""
        # Setup
        mock_bedrock_processor.call_bedrock_converse.return_value = error_scenarios['invalid_response']
        mock_bedrock_processor.extract_tool_response.side_effect = Exception("Invalid response format")
        
        # Execute and verify
        with pytest.raises(Exception, match="Invalid response format"):
            await get_document_types_with_bedrock(mock_bedrock_processor, sample_combined_text)


class TestMainFunction:
    """Test cases for the main function."""
    
    @pytest.mark.asyncio
    async def test_successful_classification_pdf(self, mock_settings, mock_textract_processor, 
                                                mock_bedrock_processor, sample_s3_uris, 
                                                sample_classification_result):
        """Test successful classification of a PDF document."""
        # Setup
        s3_uri = sample_s3_uris['pdf']
        
        with patch('app.llm.classification.TextractProcessor', return_value=mock_textract_processor), \
             patch('app.llm.classification.BedrockProcessor', return_value=mock_bedrock_processor), \
             patch('app.llm.classification.get_document_types_with_bedrock', 
                   return_value=(sample_classification_result, {"response": "data"})) as mock_get_types:
            
            # Execute
            result = await main(s3_uri)
            
            # Verify structure
            assert 'classification_result' in result
            assert 'textract_response_object' in result
            assert 'bedrock_response_object' in result
            assert 'execution_info' in result
            
            # Verify classification result
            assert result['classification_result'] == sample_classification_result
            
            # Verify execution info
            exec_info = result['execution_info']
            assert 'textract' in exec_info
            assert 'bedrock' in exec_info
            assert exec_info['textract']['file_path'] == s3_uri
            assert exec_info['bedrock']['model_id'] == mock_settings.MODEL_ID_CLASSIFICATION
    
    @pytest.mark.asyncio
    async def test_invalid_s3_uri_format(self, mock_settings, sample_s3_uris):
        """Test handling of invalid S3 URI format."""
        # Test with invalid URI
        with pytest.raises(ValueError, match="Only S3 URIs are supported"):
            await main(sample_s3_uris['invalid'])
    
    @pytest.mark.asyncio
    async def test_empty_file_path(self, mock_settings):
        """Test handling of empty file path."""
        with pytest.raises(ValueError, match="file_path must be a non-empty string"):
            await main("")
        
        with pytest.raises(ValueError, match="file_path must be a non-empty string"):
            await main(None)
    
    @pytest.mark.asyncio
    async def test_textract_processor_error(self, mock_settings, mock_textract_processor, 
                                           sample_s3_uris, error_scenarios):
        """Test handling of TextractProcessor errors."""
        # Setup
        mock_textract_processor.process_document_classification.side_effect = error_scenarios['textract_error']
        
        with patch('app.llm.classification.TextractProcessor', return_value=mock_textract_processor):
            # Execute and verify
            with pytest.raises(Exception, match="Textract service unavailable"):
                await main(sample_s3_uris['pdf'])
    
    @pytest.mark.asyncio
    async def test_bedrock_processor_error(self, mock_settings, mock_textract_processor, 
                                          mock_bedrock_processor, sample_s3_uris, error_scenarios):
        """Test handling of BedrockProcessor errors."""
        # Setup
        with patch('app.llm.classification.TextractProcessor', return_value=mock_textract_processor), \
             patch('app.llm.classification.BedrockProcessor', return_value=mock_bedrock_processor), \
             patch('app.llm.classification.get_document_types_with_bedrock', 
                   side_effect=error_scenarios['bedrock_error']):
            
            # Execute and verify
            with pytest.raises(Exception, match="Bedrock model not found"):
                await main(sample_s3_uris['pdf'])
    
    @pytest.mark.asyncio
    async def test_logging_setup_called(self, mock_settings, mock_textract_processor, 
                                       mock_bedrock_processor, sample_s3_uris, 
                                       sample_classification_result):
        """Test that logging is properly set up."""
        with patch('app.llm.classification.setup_logging') as mock_setup_logging, \
             patch('app.llm.classification.TextractProcessor', return_value=mock_textract_processor), \
             patch('app.llm.classification.BedrockProcessor', return_value=mock_bedrock_processor), \
             patch('app.llm.classification.get_document_types_with_bedrock', 
                   return_value=(sample_classification_result, {"response": "data"})):
            
            # Execute
            await main(sample_s3_uris['pdf'])
            
            # Verify
            mock_setup_logging.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_processor_initialization_with_settings(self, mock_settings, sample_s3_uris, 
                                                         sample_classification_result):
        """Test that processors are initialized with correct settings."""
        with patch('app.llm.classification.TextractProcessor') as mock_textract_class, \
             patch('app.llm.classification.BedrockProcessor') as mock_bedrock_class, \
             patch('app.llm.classification.get_document_types_with_bedrock', 
                   return_value=(sample_classification_result, {"response": "data"})):
            
            # Setup mock instances
            mock_textract_instance = MagicMock()
            mock_bedrock_instance = MagicMock()
            mock_textract_class.return_value = mock_textract_instance
            mock_bedrock_class.return_value = mock_bedrock_instance
            
            mock_textract_instance.process_document_classification.return_value = (
                "sample text", [{"response": "data"}]
            )
            
            # Execute
            await main(sample_s3_uris['pdf'])
            
            # Verify initialization
            mock_textract_class.assert_called_once()
            mock_bedrock_class.assert_called_once_with(aws_region=mock_settings.AWS_REGION_CLASSIFICATION)
    
    @pytest.mark.asyncio
    async def test_result_structure_completeness(self, mock_settings, mock_textract_processor, 
                                                mock_bedrock_processor, sample_s3_uris, 
                                                sample_classification_result):
        """Test that the result contains all required fields."""
        with patch('app.llm.classification.TextractProcessor', return_value=mock_textract_processor), \
             patch('app.llm.classification.BedrockProcessor', return_value=mock_bedrock_processor), \
             patch('app.llm.classification.get_document_types_with_bedrock', 
                   return_value=(sample_classification_result, {"bedrock": "response"})):
            
            # Execute
            result = await main(sample_s3_uris['pdf'])
            
            # Verify all required top-level keys
            required_keys = ['classification_result', 'textract_response_object', 
                           'bedrock_response_object', 'execution_info']
            for key in required_keys:
                assert key in result, f"Missing required key: {key}"
            
            # Verify execution_info structure
            exec_info = result['execution_info']
            assert 'textract' in exec_info
            assert 'bedrock' in exec_info
            
            # Verify textract execution info
            textract_info = exec_info['textract']
            required_textract_keys = ['aws_region', 'file_path', 'bucket_name']
            for key in required_textract_keys:
                assert key in textract_info, f"Missing textract execution info key: {key}"
            
            # Verify bedrock execution info
            bedrock_info = exec_info['bedrock']
            required_bedrock_keys = ['aws_region', 'model_id', 'system_prompt', 'user_prompt', 
                                   'tool_call', 'reasoning_effort', 'temperature', 'max_tokens']
            for key in required_bedrock_keys:
                assert key in bedrock_info, f"Missing bedrock execution info key: {key}"
