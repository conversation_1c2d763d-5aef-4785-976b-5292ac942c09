# General Configuration
PROJECT_NAME=your-project-name
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=local
RELOAD=False
SECRET_KEY=project-secret
BACKEND_CORS_ORIGINS=["http://yourfrontenddomain.com"]
SHOW_SWAGGER_DOC = True

# AWS Configuration
AWS_REGION=us-east-1
AWS_DEFAULT_REGION=us-east-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=CuPCJr6rRNoGO+yOQLkNp6Wbd9kVMTzaLGxc6XmT
# AWS_SESSION_TOKEN=your_session_token

# S3 Configuration
S3_BUCKET_NAME=document-extraction-logistically
EXTRACTION_FOLDER_INPUT_PREFIX='input/document-extraction-input'
EXTRACTION_FOLDER_OUTPUT_PREFIX='output/document-extraction-output'
CLASSIFICATION_FOLDER_INPUT_PREFIX='input/document-classification-input'
CLASSIFICATION_FOLDER_OUTPUT_PREFIX='output/document-classification-output'
S3_ENDPOINT_URL=
# SQS Configuration (for queue processing)
SQS_EXTRACTION_QUEUE_URL='https://sqs.us-east-1.amazonaws.com/270373480468/s3-upload-events-queue'
SQS_CLASSIFICATION_QUEUE_URL='https://sqs.us-east-1.amazonaws.com/270373480468/classification-queue'

# OpenSearch Configuration
OPENSEARCH_VECTOR_INDEX=dev_vector_index
OPENSEARCH_URL=https://localhost:9200
OPENSEARCH_USERNAME=os_uname
OPENSEARCH_PASSWORD=os_password

# Tracing Configuration
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your-api-key
LANGCHAIN_PROJECT=your-project-name

# OpenAI Configuration (Optional)
OPENAI_API_KEY=your_api_key

# Azure OpenAI Configuration (Optional)
AZURE_OPENAI_DEPLOYMENT_ENDPOINT=https://x.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_VERSION=2023-05-15
AZURE_OPENAI_API_KEY=
AZURE_OPENAI_GPT35T_DEPLOYMENT_NAME=azure_apa_genai_gpt_35_turbo
AZURE_OPENAI_GPT35T16K_DEPLOYMENT_NAME=azure_apa_genai_gpt_35_turbo_16k
AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME=azure_apa_genai_text_embedding_ada_002

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379
REDIS_PORT=6379
REDIS_TTL=604800

# CloudWatch Configuration
AWS_CLOUDWATCH_LOG_GROUP=your-cloudwatch-log-group
AWS_CLOUDWATCH_LOG_STREAM=your-cloudwatch-log-group-ls

#LANGFUSE
LANGFUSE_ENABLED=true
LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_PUBLIC_KEY=pk-lf-c6ae052a-2c38-4a5d-bd12-fc930882ead1
LANGFUSE_HOST=https://langfuse.armakuni.in
LANGFUSE_FLUSH_AT=1
LANGFUSE_FLUSH_INTERVAL=1000
LANGFUSE_PROMPT_LABEL=development

#LLM
TEMPERATURE = 0.2
TOP_P = 0.5
MAX_TOKENS = 4000

# Bedrock Configuration

# Extraction
MODEL_ID_EXTRACTION=amazon.nova-pro-v1:0
AWS_REGION_EXTRACTION=us-east-1
TEMPERATURE_EXTRACTION=0.2
TOP_P_EXTRACTION=0.5
MAX_TOKENS_EXTRACTION=500

# Classification
MODEL_ID_CLASSIFICATION=openai.gpt-oss-20b-1:0
AWS_REGION_CLASSIFICATION=us-west-2
TEMPERATURE_CLASSIFICATION=0.7
MAX_TOKENS_CLASSIFICATION=5000
REASONING_EFFORT_CLASSIFICATION=medium