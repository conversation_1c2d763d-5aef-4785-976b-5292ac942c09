#!/usr/bin/env python3
"""
Document Classification Processor - S3 Only

This module classifies document from S3:
1. Takes S3 URI as input (PDF, JPG, JPEG, PNG, TIF, TIFF)
2. For PDFs: Downloads, splits into individual pages, processes in parallel
3. For images: Processes directly from S3
4. Processes pages/images with AWS Textract (sync with async fallback) in parallel
5. Merges all page results with <page1></page1> tags
6. Uses AWS Bedrock Converse API with OpenAI models to analyze document types
7. Returns structured response objects
"""
# Standard library imports
import json
import logging
import asyncio
from typing import Dict, Tuple

# Local imports
from app.core.configuration import settings
from app.utils.textract import TextractProcessor
from app.llm.bedrock import BedrockProcessor
from app.prompts.prompt_classification import SYSTEM_PROMPT_CLASSIFICATION, TOOL_CALL_CLASSIFICATION

# Constants
TEMP_BUCKET_NAME = settings.S3_BUCKET_NAME
TEMP_PREFIX = settings.TEXTRACT_TEMP_PREFIX


def setup_logging() -> logging.Logger:
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    return logger


async def get_document_types_with_bedrock(bedrock_processor: BedrockProcessor, combined_text: str) -> Tuple[Dict, Dict]:
    """
    Get document types using AWS Bedrock with structured tool calling.

    This function uses the OpenAI model via Bedrock to classify logistics documents
    into predefined categories using structured tool calling for reliable output.

    Features:
    - Uses predefined system prompts for consistent classification
    - Structured tool calling ensures reliable JSON output format
    - Configurable reasoning effort and temperature settings
    - Comprehensive error handling for production use

    Args:
        bedrock_processor: Initialized BedrockProcessor instance with proper credentials
        combined_text: Combined text from all document pages with <pageN></pageN> tags
                      for multi-page document processing

    Returns:
        Tuple containing:
            - analysis_result (Dict): Structured classification results with page_no and doc_type
            - full_bedrock_response (Dict): Complete Bedrock API response for debugging

    Raises:
        Exception: If Bedrock API call fails or response format is unexpected
    """
    # Use model from configuration
    model_id = settings.MODEL_ID_CLASSIFICATION

    # Prepare messages for Bedrock
    messages = [
        {
            "role": "user",
            "content": [{"text": combined_text}]
        }
    ]

    logging.info(f"Trying Bedrock model: {model_id}")

    # Prepare inference config with classification settings
    inference_config = {
        'temperature': settings.TEMPERATURE_CLASSIFICATION,
        'maxTokens': settings.MAX_TOKENS_CLASSIFICATION
    }

    # Additional model request fields
    additionalModelRequestFields = {
        "reasoning_effort": settings.REASONING_EFFORT_CLASSIFICATION
    }

    # Call Bedrock using helper method with loaded prompts
    response = bedrock_processor.call_bedrock_converse(
        model_id=model_id,
        system_prompt=SYSTEM_PROMPT_CLASSIFICATION,
        messages=messages,
        inference_config=inference_config,
        tool_config=TOOL_CALL_CLASSIFICATION,
        additionalModelRequestFields=additionalModelRequestFields
    )

    # Extract tool response using helper method
    content = bedrock_processor.extract_tool_response(response)
    return content, response


async def main(file_path: str) -> Dict:
    """
    Document classification processor for logistics documents.

    This function provides a complete document classification pipeline:
    1. S3 URI validation and parsing
    2. Document type detection (PDF vs image)
    3. Multi-page PDF processing with parallel Textract calls
    4. Text extraction and page tagging for context preservation
    5. AWS Bedrock classification with structured output
    6. Comprehensive result compilation with metadata

    Args:
        file_path: Valid S3 URI pointing to the document to classify.
                  Must be in format 's3://bucket/key'.
                  Supported formats: PDF, JPG, JPEG, PNG, TIF, TIFF.

    Returns:
        Dictionary containing:
            - 'classification_result': Main classification results with page_no and doc_type
            - 'textract_response_object': List of raw Textract response objects for each page
            - 'bedrock_response_object': Complete Bedrock response object for debugging
            - 'execution_info': Metadata about processing configuration and parameters

    Raises:
        ValueError: If file_path is invalid or unsupported file type
        Exception: For processing failures during Textract or Bedrock operations
    """
    # Setup logging first
    logger = setup_logging()

    # Input validation
    if not file_path or not isinstance(file_path, str):
        raise ValueError("file_path must be a non-empty string")

    # Validate S3 URI format
    if not file_path.startswith('s3://'):
        raise ValueError(f"Only S3 URIs are supported. Expected format: s3://bucket/key, got: {file_path}")


    try:
        # Initialize processors with classification settings
        textract_processor = TextractProcessor()
        bedrock_processor = BedrockProcessor(aws_region=settings.AWS_REGION_CLASSIFICATION)

        # Process document with Textract
        logger.info(f"Processing document from S3: {file_path}")
        combined_text, textract_responses = textract_processor.process_document_classification(
            file_path, TEMP_BUCKET_NAME, TEMP_PREFIX
        )

        # Analyze document types with Bedrock using loaded prompts
        logger.info("Analyzing document types with Bedrock...")
        classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)

        result = {
            'classification_result': classification_result,
            'textract_response_object': textract_responses,
            'bedrock_response_object': bedrock_response,
            'execution_info': {
                'textract': {
                    'aws_region': settings.AWS_REGION_EXTRACTION,
                    'file_path': file_path,
                    'bucket_name': TEMP_BUCKET_NAME
                },
                'bedrock' : {
                    'aws_region': settings.AWS_REGION_CLASSIFICATION,
                    'model_id': settings.MODEL_ID_CLASSIFICATION,
                    'system_prompt': SYSTEM_PROMPT_CLASSIFICATION,
                    'user_prompt': combined_text,
                    'tool_call': TOOL_CALL_CLASSIFICATION,
                    'reasoning_effort': settings.REASONING_EFFORT_CLASSIFICATION,
                    'temperature': settings.TEMPERATURE_CLASSIFICATION,
                    'max_tokens': settings.MAX_TOKENS_CLASSIFICATION,
                }
            }
        }

        logger.info(f"Successfully processed S3 document: {file_path}")
        return result

    except Exception as e:
        logger.error(f"Processing failed for {file_path}: {e}")
        raise


if __name__ == "__main__":

    # LOADING S3_SAMPLE_FILE_CLASSIFICATION FROM ENV
    S3_SAMPLE_FILE_CLASSIFICATION = settings.S3_SAMPLE_FILE_CLASSIFICATION

    result = asyncio.run(main(S3_SAMPLE_FILE_CLASSIFICATION))
    print(json.dumps(result["classification_result"], indent=4))
