"""
Async S3 Service for handling file operations with AWS S3 (using aioboto3).
"""
import os
import tempfile
from typing import List, Optional, Dict, Any
from io import BytesIO
import aioboto3
from botocore.exceptions import ClientError
from fastapi import UploadFile, HTTPException, status
from loguru import logger

from app.core.configuration import settings


class S3Service:
    """
    Async service class for handling S3 operations with proper error handling and logging.
    """

    def __init__(self):
        """Initialize parameters (session created on demand)."""
        self.aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
        self.aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None
        self.aws_region = settings.AWS_REGION
        # self.aws_session_token = settings.AWS_SESSION_TOKEN or None

    def _get_session(self):
        """Return a new aioboto3 session."""
        return aioboto3.Session()
    
    
    async def upload_file_from_bytes(
        self,
        bucket_name: str,
        object_key: str,
        content_bytes: bytes,
        content_type: str = "application/octet-stream",
    ) -> bool:
        """
        Upload raw bytes as a file to S3.
        
        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key
            content_bytes: Raw file bytes
            content_type: MIME type of the file
            
        Returns:
            bool: True if upload successful
        """
        try:
            session = aioboto3.Session()
            async with session.client(
                "s3",
                aws_access_key_id=self.aws_access_key_id,
                aws_secret_access_key=self.aws_secret_access_key,
                region_name=settings.AWS_REGION,
            ) as s3_client:
                await s3_client.put_object(
                    Bucket=bucket_name,
                    Key=object_key,
                    Body=BytesIO(content_bytes),
                    ContentType=content_type,
                )
            logger.info(f"[S3] Uploaded bytes to s3://{bucket_name}/{object_key}")
            return True
        except Exception as e:
            logger.error(f"[S3] Failed to upload bytes: {e}")
            return False

    async def create_bucket(self, bucket_name: str) -> bool:
        """
        Create an S3 bucket if it doesn't exist.
        """
        session = self._get_session()
        async with session.client(
            "s3",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region,
        ) as s3_client:
            try:
                await s3_client.head_bucket(Bucket=bucket_name)
                logger.info(f"[S3] Bucket {bucket_name} already exists")
                return True
            except ClientError as e:
                code = e.response["Error"]["Code"]
                if code in ["404", "NoSuchBucket"]:
                    try:
                        await s3_client.create_bucket(
                            Bucket=bucket_name,
                            CreateBucketConfiguration=(
                                {"LocationConstraint": self.aws_region}
                                if self.aws_region != "us-east-1"
                                else {}
                            ),
                        )
                        logger.info(f"[S3] Created bucket {bucket_name}")
                        return True
                    except ClientError as ce:
                        logger.error(f"[S3] Failed to create bucket {bucket_name}: {ce}")
                        return False
                elif code == "403":
                    logger.warning(f"[S3] Access denied to bucket {bucket_name}")
                    return False
                else:
                    logger.error(f"[S3] Error checking bucket {bucket_name}: {e}")
                    return False

    async def upload_file(
        self,
        file: UploadFile,
        bucket_name: str,
        folder_prefix: str = "",
        custom_filename: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Upload a file to S3 bucket.
        """
        if not file:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="No file provided"
            )

        filename = custom_filename or file.filename
        if not filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="No filename provided"
            )

        object_key = os.path.join(folder_prefix, filename).replace("\\", "/")

        session = self._get_session()
        async with session.client(
            "s3",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region,
        ) as s3_client:
            try:
                await s3_client.upload_fileobj(
                    file.file,
                    bucket_name,
                    object_key,
                    ExtraArgs={
                        "ContentType": file.content_type or "application/octet-stream"
                    },
                )

                presigned_url = await s3_client.generate_presigned_url(
                    "get_object",
                    Params={"Bucket": bucket_name, "Key": object_key},
                    ExpiresIn=getattr(settings, "S3_URL_EXPIRY", 3600),
                )

                result = {
                    "bucket_name": bucket_name,
                    "object_key": object_key,
                    "filename": filename,
                    "file_size": getattr(file, "size", None),
                    "content_type": file.content_type,
                    "s3_url": f"s3://{bucket_name}/{object_key}",
                    "presigned_url": presigned_url,
                }

                logger.info(f"[S3] Uploaded {filename} to {bucket_name}/{object_key}")
                return result

            except ClientError as e:
                logger.error(f"[S3] Upload error: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to upload file: {str(e)}",
                )

    async def download_file(self, bucket_name: str, object_key: str) -> tempfile.NamedTemporaryFile:
        """
        Download a file from S3 to a temporary file.
        """
        session = self._get_session()
        async with session.client(
            "s3",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region,
        ) as s3_client:
            try:
                temp_file = tempfile.NamedTemporaryFile(delete=True)
                await s3_client.download_fileobj(bucket_name, object_key, temp_file)
                temp_file.seek(0)
                logger.info(f"[S3] Downloaded {object_key} from {bucket_name}")
                return temp_file
            except ClientError as e:
                logger.error(f"[S3] Download error: {e}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"File not found: {object_key}",
                )

    async def list_files(
        self, bucket_name: str, folder_prefix: str = "", max_keys: int = 1000
    ) -> List[Dict[str, Any]]:
        """
        List files in an S3 bucket with optional folder prefix.
        """
        session = self._get_session()
        async with session.client(
            "s3",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region,
        ) as s3_client:
            try:
                files: List[Dict[str, Any]] = []
                paginator = s3_client.get_paginator("list_objects_v2")

                page_iterator = paginator.paginate(
                    Bucket=bucket_name,
                    PaginationConfig={"MaxItems": max_keys},
                    Prefix=folder_prefix or "",
                )

                async for page in page_iterator:
                    for obj in page.get("Contents", []):
                        files.append(
                            {
                                "key": obj["Key"],
                                "size": obj["Size"],
                                "last_modified": obj["LastModified"].isoformat(),
                                "etag": obj["ETag"].strip('"'),
                                "storage_class": obj.get("StorageClass", "STANDARD"),
                            }
                        )

                logger.info(f"[S3] Found {len(files)} files in {bucket_name}/{folder_prefix}")
                return files

            except ClientError as e:
                logger.error(f"[S3] List error: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to list files: {str(e)}",
                )

    async def delete_file(self, bucket_name: str, object_key: str) -> bool:
        """
        Delete a file from S3 bucket.
        """
        session = self._get_session()
        async with session.client(
            "s3",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region,
        ) as s3_client:
            try:
                await s3_client.delete_object(Bucket=bucket_name, Key=object_key)
                logger.info(f"[S3] Deleted {object_key} from {bucket_name}")
                return True
            except ClientError as e:
                logger.error(f"[S3] Delete error: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to delete file: {str(e)}",
                )

    async def get_file_url(
        self, bucket_name: str, object_key: str, expires_in: int = 3600
    ) -> str:
        """
        Generate a presigned URL for file access.
        """
        session = self._get_session()
        async with session.client(
            "s3",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region,
        ) as s3_client:
            try:
                return await s3_client.generate_presigned_url(
                    "get_object",
                    Params={"Bucket": bucket_name, "Key": object_key},
                    ExpiresIn=expires_in,
                )
            except ClientError as e:
                logger.error(f"[S3] Presigned URL error: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to generate file access URL",
                )

    async def check_file_exists(self, bucket_name: str, object_key: str) -> bool:
        """
        Check if a file exists in S3 bucket.
        """
        session = self._get_session()
        async with session.client(
            "s3",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region,
        ) as s3_client:
            try:
                await s3_client.head_object(Bucket=bucket_name, Key=object_key)
                return True
            except ClientError as e:
                code = e.response["Error"]["Code"]
                if code in ["404", "NoSuchKey"]:
                    return False
                logger.error(f"[S3] File existence check error: {e}")
                return False


# Global instance
s3_service = S3Service()
