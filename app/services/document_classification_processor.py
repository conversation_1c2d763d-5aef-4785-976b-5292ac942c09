"""
Async Worker to process S3 events from an SQS queue (classification pipeline).
"""
import sys
import json
import asyncio
from io import Bytes<PERSON>
from pathlib import Path
import os
from typing import Dict, Any, Optional
from datetime import datetime
from pypdf import PdfReader, PdfWriter
from PIL import Image

import aioboto3
from botocore.exceptions import ClientError
import urllib.parse
from loguru import logger

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(project_root))

from app.core.configuration import settings
from app.services.s3_service import s3_service
from app.llm.classification import main as ai_classification_service


class DocumentClassificationProcessor:
    def __init__(self, queue_url: str, region_name: str):
        if not queue_url:
            raise ValueError("SQS_QUEUE_URL is required.")
        self.queue_url = queue_url
        self.region_name = region_name
        self.aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
        self.aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None
        self.output_folder = settings.CLASSIFICATION_FOLDER_OUTPUT_PREFIX
        logger.info(f"[Classification Worker] Initialized for queue: {queue_url}")

    async def process_messages(self):
        session = aioboto3.Session()
        async with session.client(
            "sqs",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.region_name,
        ) as sqs_client:
            while True:
                try:
                    response = await sqs_client.receive_message(
                        QueueUrl=self.queue_url,
                        MaxNumberOfMessages=10,
                        WaitTimeSeconds=20,
                        MessageAttributeNames=["All"],
                    )
                    messages = response.get("Messages", [])
                    if not messages:
                        logger.debug("[Classification Worker] No new messages.")
                        continue

                    logger.info(f"[Classification Worker] Got {len(messages)} messages.")
                    await asyncio.gather(
                        *[self._process_single_message(m, sqs_client) for m in messages]
                    )
                except ClientError as e:
                    logger.error(f"[Classification Worker] SQS error: {e}")
                    await asyncio.sleep(10)

    async def _process_single_message(self, message: Dict[str, Any], sqs_client):
        receipt_handle = message["ReceiptHandle"]
        try:
            body = json.loads(message["Body"])
            s3_event = json.loads(body["Message"]) if "Message" in body else body

            if "Records" not in s3_event:
                logger.warning("[Classification Worker] Invalid message, skipping.")
                await self._delete_message(receipt_handle, sqs_client)
                return

            tasks = []
            for record in s3_event["Records"]:
                bucket = record["s3"]["bucket"]["name"]
                key = urllib.parse.unquote_plus(record["s3"]["object"]["key"])
                logger.info(f"[Classification Worker] Queueing {key}")
                tasks.append(self._handle_ingestion(bucket, key))

            results = await asyncio.gather(*tasks, return_exceptions=False)

            if all(results):
                await self._delete_message(receipt_handle, sqs_client)
                logger.info("[Classification Worker] All ingestions succeeded, message deleted.")
            else:
                logger.error("[Classification Worker] Some ingestions failed — message NOT deleted, will retry.")

        except Exception as e:
            logger.error(f"[Classification Worker] Failed message: {e}")

    async def _delete_message(self, receipt_handle: str, sqs_client):
        try:
            await sqs_client.delete_message(
                QueueUrl=self.queue_url, ReceiptHandle=receipt_handle
            )
            logger.info("[Classification Worker] Deleted message from queue.")
        except ClientError as e:
            logger.error(f"[Classification Worker] Delete failed: {e}")

    async def _handle_ingestion(self, bucket: str, key: str) -> bool:
        """
        Orchestrates ingestion: download file, classify, split, upload outputs + JSON.
        """
        try:
            s3_uri = f"s3://{bucket}/{key}"
            classification_result = await ai_classification_service(s3_uri)
            classification = classification_result["classification_result"]

            # Download original file
            temp_file = await s3_service.download_file(bucket, key)
            ext = os.path.splitext(key)[1].lower()

            # Group docs by type
            grouped_docs = self._group_documents_by_type(temp_file, ext, classification)
            temp_file.close()  # cleanup
            if grouped_docs is None:
                return False

            # Build base output folder
            output_base_folder = self._build_output_folder(key)

            # Upload split files
            uploaded_files = await self._upload_grouped_docs(bucket, grouped_docs, ext, output_base_folder)
            if not uploaded_files:
                return False

            logger.info(f"[Classification Worker] Processed {len(uploaded_files)} files for {key}")

            # Upload classification JSON
            return await self._upload_classification_json(bucket, key, s3_uri, classification_result, output_base_folder, uploaded_files)

        except Exception as e:
            logger.error(f"[Classification Worker] Ingestion failed for {key}: {e}")
            raise

    def _group_documents_by_type(self, temp_file, ext: str, classification: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Groups pages/images by document type based on classification.
        Returns a dict: {doc_type: PdfWriter | [PIL.Image]}
        """
        grouped_docs: Dict[str, Any] = {}

        if ext.lower() == ".pdf":
            reader = PdfReader(temp_file)
            for doc in classification["documents"]:
                page_no = doc["page_no"] - 1
                doc_type = doc["doc_type"]
                grouped_docs.setdefault(doc_type, PdfWriter()).add_page(reader.pages[page_no])

        elif ext.lower() in [".jpg", ".jpeg", ".png"]:
            with Image.open(temp_file) as image:
                for doc in classification["documents"]:
                    doc_type = doc["doc_type"]
                    grouped_docs.setdefault(doc_type, []).append(image.copy())

        elif ext.lower() in [".tif", ".tiff"]:
            with Image.open(temp_file) as tiff:
                for page_no, doc in enumerate(classification["documents"]):
                    doc_type = doc["doc_type"]
                    tiff.seek(page_no)
                    grouped_docs.setdefault(doc_type, []).append(tiff.copy())

        else:
            raise ValueError(f"Unsupported file type: {ext}")

        return grouped_docs

    def _build_output_folder(self, key: str) -> str:
        """
        Build base folder path inside output bucket from input key.
        """
        path_components = key.split("/")
        if len(path_components) > 2:
            path_after_prefix = "/".join(path_components[2:])
        else:
            path_after_prefix = path_components[-1]
        base_path, _ = os.path.splitext(path_after_prefix)
        return f"{self.output_folder}/{base_path}"

    async def _upload_grouped_docs(self, bucket: str, grouped_docs: Dict[str, Any], ext: str, output_base_folder: str) -> list:
        """
        Uploads grouped PDFs or images to S3.
        Returns list of uploaded file keys.
        """
        uploaded_files = []
        for doc_type, content in grouped_docs.items():
            if ext == ".pdf":
                output_stream = BytesIO()
                content.write(output_stream)
                output_stream.seek(0)
                key = f"{output_base_folder}/{doc_type}.pdf"
                success = await s3_service.upload_file_from_bytes(
                    bucket_name=bucket,
                    object_key=key,
                    content_bytes=output_stream.read(),
                    content_type="application/pdf",
                )
                if success:
                    uploaded_files.append(key)
                else:
                    logger.error(f"[Classification Worker] Failed to upload {key}")
                    return []

            else:  # images
                # Normalize format from extension
                img_format = ext.replace(".", "").upper()
                if img_format == "JPG":
                    img_format = "JPEG"

                for idx, img in enumerate(content, start=1):
                    output_stream = BytesIO()
                    img.save(output_stream, format=img_format)
                    output_stream.seek(0)

                    # Use base filename + page number instead of just index
                    key = f"{output_base_folder}/{doc_type}{ext}"
                    success = await s3_service.upload_file_from_bytes(
                        bucket_name=bucket,
                        object_key=key,
                        content_bytes=output_stream.read(),
                        content_type=f"image/{img_format.lower()}",
                    )
                    if success:
                        uploaded_files.append(key)
                    else:
                        logger.error(f"[Classification Worker] Failed to upload {key}")
                        return []

        return uploaded_files

    async def _upload_classification_json(
        self,
        bucket: str,
        key: str,
        s3_uri: str,
        classification: Dict[str, Any],
        output_base_folder: str,
        uploaded_files: list,
    ) -> bool:
        """
        Uploads enriched classification.json alongside outputs.
        """
        classification_json = {
            "s3_input_uri": s3_uri,
            "input_key": key,
            "output_base_folder": output_base_folder,
            "timestamp": datetime.utcnow().isoformat(),
            "output_files": uploaded_files,
            "classification": classification,
        }
        json_bytes = json.dumps(classification_json, indent=4).encode("utf-8")
        success = await s3_service.upload_file_from_bytes(
            bucket_name=bucket,
            object_key=f"{output_base_folder}/classification.json",
            content_bytes=json_bytes,
            content_type="application/json",
        )
        if not success:
            logger.error(f"[Classification Worker] Failed to upload classification.json")
            return False

        return True


if __name__ == "__main__":
    worker = DocumentClassificationProcessor(
        queue_url=settings.SQS_CLASSIFICATION_QUEUE_URL,
        region_name=settings.AWS_REGION,
    )
    asyncio.run(worker.process_messages())
