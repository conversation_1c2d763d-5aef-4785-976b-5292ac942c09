"""
S3 endpoints for file operations.
"""
from typing import List, Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, status
from fastapi.responses import StreamingResponse
from loguru import logger
from uuid import uuid4

from app.services.s3_service import s3_service
from app.schemas.s3 import (
    S3UploadResponse,
    S3ListResponse,
    S3DeleteResponse,
    S3PresignedUrlRequest,
    S3PresignedUrlResponse,
    S3HealthCheck,
    S3BucketInfo
)
from app.core.configuration import settings

router = APIRouter()



@router.post("/upload-files", status_code=status.HTTP_201_CREATED)
async def upload_multiple_files_unique(
    files: List[UploadFile] = File(..., description="Files to upload")
):
    """
    Upload multiple files to S3 with unique names to avoid overwrites.
    Each file is stored under a UUID-based prefix to guarantee uniqueness.
    """
    results = []
    bucket_name = settings.S3_BUCKET_NAME
    folder_prefix = settings.EXTRACTION_FOLDER_INPUT_PREFIX

    try:
        for file in files:
            if not file.filename:
                results.append({
                    "success": False,
                    "error": "Missing filename",
                    "original_filename": None
                })
                continue

            original_filename = file.filename
            guid = uuid4().hex

            # Build S3 key
            s3_key = f"{folder_prefix}/{guid}/{original_filename}"
            s3_uri = f"s3://{bucket_name}/{s3_key}"

            try:
                # Use our S3 service for upload
                upload_result = await s3_service.upload_file(
                    file=file,
                    bucket_name=bucket_name,
                    folder_prefix=f"{folder_prefix}/{guid}",
                    custom_filename=original_filename
                )

                results.append({
                    "success": True,
                    "s3_key": upload_result["object_key"],
                    "s3_uri": upload_result["s3_url"],
                    "presigned_url": upload_result["presigned_url"]
                })

            except Exception as e:
                logger.error(f"Error uploading {original_filename}: {e}")
                results.append({
                    "success": False,
                    "original_filename": original_filename,
                    "error": str(e)
                })

        return {
            "total_files": len(files),
            "successful_uploads": len([r for r in results if r["success"]]),
            "failed_uploads": len([r for r in results if not r["success"]]),
            "results": results,
        }

    except Exception as e:
        logger.error(f"Unexpected error in multiple upload: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during multiple file upload"
        )



@router.post("/upload", response_model=S3UploadResponse, status_code=status.HTTP_201_CREATED)
async def upload_file(
    file: UploadFile = File(..., description="File to upload"),
    bucket_name: str = Form(..., description="Target S3 bucket name"),
    folder_prefix: str = Form(default="", description="Optional folder prefix"),
    custom_filename: Optional[str] = Form(None, description="Optional custom filename")
):
    """
    Upload a file to S3 bucket.
    
    - **file**: The file to upload
    - **bucket_name**: Target S3 bucket name
    - **folder_prefix**: Optional folder prefix within the bucket
    - **custom_filename**: Optional custom filename (uses original if not provided)
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No filename provided"
            )
        
        # Upload file to S3
        upload_result = s3_service.upload_file(
            file=file,
            bucket_name=bucket_name,
            folder_prefix=folder_prefix,
            custom_filename=custom_filename
        )
        
        # Convert to response model
        response = S3UploadResponse(
            success=True,
            **upload_result
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in upload endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during file upload"
        )


@router.post("/upload-multiple", status_code=status.HTTP_201_CREATED)
async def upload_multiple_files(
    files: List[UploadFile] = File(..., description="Files to upload"),
    bucket_name: str = Form(..., description="Target S3 bucket name"),
    folder_prefix: str = Form(default="", description="Optional folder prefix")
):
    """
    Upload multiple files to S3 bucket.
    
    - **files**: List of files to upload
    - **bucket_name**: Target S3 bucket name
    - **folder_prefix**: Optional folder prefix within the bucket
    """
    try:
        results = []
        for file in files:
            try:
                upload_result = s3_service.upload_file(
                    file=file,
                    bucket_name=bucket_name,
                    folder_prefix=folder_prefix
                )
                results.append({
                    "filename": file.filename,
                    "success": True,
                    **upload_result
                })
            except Exception as e:
                logger.error(f"Failed to upload {file.filename}: {e}")
                results.append({
                    "filename": file.filename,
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "total_files": len(files),
            "successful_uploads": len([r for r in results if r["success"]]),
            "failed_uploads": len([r for r in results if not r["success"]]),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Unexpected error in multiple upload endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during multiple file upload"
        )


@router.get("/list/{bucket_name}", response_model=S3ListResponse)
async def list_files(
    bucket_name: str,
    folder_prefix: str = "",
    max_keys: int = 1000
):
    """
    List files in an S3 bucket.
    
    - **bucket_name**: S3 bucket name
    - **folder_prefix**: Optional folder prefix to filter results
    - **max_keys**: Maximum number of keys to return (default: 1000)
    """
    try:
        files = s3_service.list_files(
            bucket_name=bucket_name,
            folder_prefix=folder_prefix,
            max_keys=max_keys
        )
        
        # Convert to response model
        file_infos = [
            {
                "key": file["key"],
                "size": file["size"],
                "last_modified": file["last_modified"],
                "etag": file["etag"],
                "storage_class": file["storage_class"]
            }
            for file in files
        ]
        
        response = S3ListResponse(
            bucket_name=bucket_name,
            folder_prefix=folder_prefix,
            files=file_infos,
            total_count=len(files),
            max_keys=max_keys
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in list files endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while listing files"
        )


@router.get("/download/{bucket_name}/{object_key:path}")
async def download_file(bucket_name: str, object_key: str):
    """
    Download a file from S3 bucket.
    
    - **bucket_name**: S3 bucket name
    - **object_key**: S3 object key (can include path)
    """
    try:
        temp_file = s3_service.download_file(bucket_name, object_key)
        
        # Get filename from object key
        filename = object_key.split('/')[-1]
        
        return StreamingResponse(
            temp_file,
            media_type='application/octet-stream',
            headers={
                'Content-Disposition': f'attachment; filename="{filename}"'
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in download endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during file download"
        )


@router.delete("/delete", response_model=S3DeleteResponse)
async def delete_file(
    bucket_name: str = Form(..., description="S3 bucket name"),
    object_key: str = Form(..., description="S3 object key to delete")
):
    """
    Delete a file from S3 bucket.
    
    - **bucket_name**: S3 bucket name
    - **object_key**: S3 object key to delete
    """
    try:
        success = s3_service.delete_file(bucket_name, object_key)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete file"
            )
        
        response = S3DeleteResponse(
            success=True,
            bucket_name=bucket_name,
            object_key=object_key
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in delete endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during file deletion"
        )


@router.post("/presigned-url", response_model=S3PresignedUrlResponse)
async def get_presigned_url(request: S3PresignedUrlRequest):
    """
    Generate a presigned URL for file access.
    
    - **bucket_name**: S3 bucket name
    - **object_key**: S3 object key
    - **expires_in**: URL expiration time in seconds (1 hour to 7 days)
    """
    try:
        presigned_url = s3_service.get_file_url(
            bucket_name=request.bucket_name,
            object_key=request.object_key,
            expires_in=request.expires_in
        )
        
        response = S3PresignedUrlResponse(
            bucket_name=request.bucket_name,
            object_key=request.object_key,
            presigned_url=presigned_url,
            expires_in=request.expires_in
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in presigned URL endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while generating presigned URL"
        )


@router.get("/bucket/{bucket_name}", response_model=S3BucketInfo)
async def get_bucket_info(bucket_name: str):
    """
    Get information about an S3 bucket.
    
    - **bucket_name**: S3 bucket name
    """
    try:
        exists = s3_service.create_bucket(bucket_name)  # This checks if bucket exists
        
        response = S3BucketInfo(
            name=bucket_name,
            region=settings.AWS_REGION,
            exists=exists
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Unexpected error in bucket info endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while getting bucket info"
        )


@router.get("/health", response_model=S3HealthCheck)
async def health_check():
    """
    Check S3 service health and configuration.
    """
    try:
        # Check if credentials are configured
        credentials_configured = bool(
            settings.AWS_ACCESS_KEY_ID and 
            settings.AWS_SECRET_ACCESS_KEY
        )
        
        # Check default bucket accessibility if configured
        default_bucket_accessible = None
        if settings.S3_BUCKET_NAME and credentials_configured:
            try:
                default_bucket_accessible = s3_service.check_file_exists(
                    settings.S3_BUCKET_NAME, 
                    "health-check-test"
                )
            except:
                default_bucket_accessible = False
        
        response = S3HealthCheck(
            service_status="healthy" if credentials_configured else "unhealthy",
            credentials_configured=credentials_configured,
            default_bucket_accessible=default_bucket_accessible,
            region=settings.AWS_REGION
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Unexpected error in health check endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during health check"
        ) 