# Project Overview: Document Data Extraction & Classification

## 1. High-Level Goal

This project provides a robust, asynchronous pipeline for extracting structured data from documents and classifying them. It leverages cloud services and Large Language Models (LLMs) to process files uploaded to an S3 bucket, storing the extracted data in a searchable format and saving the classified documents back to S3.
## 2. Core Technologies

- **Backend Framework**: FastAPI
- **Cloud Services**:
  - AWS S3 (for document storage and output)
  - AWS SQS (for asynchronous message queuing)
  - AWS Textract (for document text extraction and OCR)
  - AWS Bedrock (for AI-powered data extraction and classification)
- **LLM Integration**:
  - **Primary**: AWS Bedrock with models like `amazon.nova-pro-v1:0` (extraction) and `openai.gpt-oss-20b-1:0` (classification)
  - **Monitoring**: Langfuse for LLM observability and tracking
- **Async Processing**: `asyncio`, `aioboto3` for non-blocking I/O operations
- **Containerization**: Docker, Docker Compose

## 3. Architecture Overview

The application follows an event-driven, asynchronous architecture with two main processing pipelines:

### Document Extraction Pipeline
1.  **Upload**: Documents (PDF, JPG, JPEG, PNG, TIF, TIFF) are uploaded to designated S3 bucket paths
2.  **Trigger**: S3 upload events trigger notifications to the `extraction` SQS queue which is processed by `extraction-worker`
3.  **Text Extraction**: The extraction worker uses **AWS Textract** to extract text and layout information from documents
4.  **AI Processing**: Extracted text is sent to **AWS Bedrock** (using models like `amazon.nova-pro-v1:0`) for structured data extraction
5.  **Output**: Results are saved as JSON files in S3 output folders

### Document Classification Pipeline
1.  **Upload**: Documents are uploaded to classification-specific S3 paths
2.  **Trigger**: S3 events trigger notifications to the `classification` SQS queue which is processed by `classification-worker`
3.  **Text Extraction**: AWS Textract processes documents
4.  **AI Classification**: **AWS Bedrock** (using models like `openai.gpt-oss-20b-1:0`) classifies document types
5.  **Document Splitting**: Based on classification, PDFs are split into separate documents by type
6.  **Output**: Classified and split documents are saved back to S3

## 4. Project Structure

- **/app**: The core FastAPI application code.
  - **/api**: API endpoints, request/response models, and dependencies
  - **/core**: Core application logic and configuration
    - `configuration.py`: Environment settings and AWS/Bedrock configurations
    - `logger.py`: Logging configuration
  - **/llm**: LLM integration and processing modules
    - `bedrock.py`: AWS Bedrock client and helper methods
    - `extraction.py`: Document data extraction pipeline using Bedrock
    - `classification.py`: Document classification pipeline using Bedrock
  - **/services**: Asynchronous worker services for document processing
    - `document_extraction_processor.py`: SQS worker for extraction pipeline
    - `document_classification_processor/`: SQS worker for classification pipeline
    - `s3_service.py`: S3 client and operations
  - **/utils**: Utility modules
    - `textract.py`: AWS Textract integration for document text extraction
    - `langfuse_util.py`: Langfuse monitoring and observability
    - `text_utils.py`: Text processing utilities
  - **/prompts**: LLM prompt templates
    - `prompt_extraction.py`: Extraction prompts
    - `prompt_classification.py`: Classification prompts
  - **`main.py`**: FastAPI application entry point
- **/scripts**: Utility scripts for setup and maintenance
- **`docker-compose.yml`**: Defines and configures all the services required to run the application stack (API, workers, OpenSearch).
- **`Dockerfile`**: Defines the container image for the Python application.
- **`env.example`**: Environment variables template

## 5. Getting Started

To run the project locally for review:

1.  **Prerequisites**: Docker and Docker Compose must be installed.
2.  **Environment**: Create a `.env` file from the `env.example` template and fill in the necessary credentials for AWS and at least one LLM provider.
3.  **Build & Run**: Execute the following command from the project root:
    ```bash
    docker-compose up -d --build
    ```
4.  **Access services**: Start the background service for extraction or classification inside app/services [eg: python document_extraction_processor.py ].

## 6. Architecture Diagram

You can render the following Mermaid code in a compatible editor (like GitHub's Markdown viewer or a free online tool) to visualize the project architecture.

```mermaid
graph TD
    %% User Interaction
    subgraph "User Interaction"
        User["User"]
    end

    %% AWS Cloud
    subgraph "AWS Cloud"
        S3Input["S3 Bucket (Input)"]
        S3Input --> S3Extraction["extraction-input/"]
        S3Input --> S3Classification["classification-input/"]

        SQSExtraction["SQS Queue (Extraction)"]
        SQSClassification["SQS Queue (Classification)"]

        S3OutputExtraction["S3 Bucket (Extraction Output)"]
        S3OutputClassification["S3 Bucket (Classification Output)"]

        subgraph "AI Services"
            Textract["AWS Textract<br/>(Text Detection)"]
            subgraph "LLM "
                BedrockNova["AWS Bedrock<br/>Extraction<br/>(Nova Pro)"]
                BedrockOpenAI["AWS Bedrock<br/>Classification<br/>(OpenAI)"]
            end
        end
    end

    %% Application Services
    subgraph "Application Services (Docker)"
        FastAPI["FastAPI API"]
        ExtractionWorker["Extraction Worker"]
        ClassificationWorker["Classification Worker"]
    end

    %% Monitoring
    subgraph "Monitoring"
        Langfuse["Langfuse"]
    end

    %% Flow
    User -- "Uploads Document" --> FastAPI
    FastAPI -- "Stores File" --> S3Input

    %% Extraction Flow
    S3Extraction -- "Triggers Event" --> SQSExtraction
    SQSExtraction -- "Consumed by" --> ExtractionWorker
    ExtractionWorker -- "Fetches Document" --> S3Extraction
    ExtractionWorker -- "1. Text Detection" --> Textract
    Textract -- "Detected Text" --> ExtractionWorker
    ExtractionWorker -- "2. Data Extraction" --> BedrockNova
    BedrockNova -- "Returns JSON" --> ExtractionWorker
    ExtractionWorker -- "Logs/Traces" --> Langfuse
    ExtractionWorker -- "Stores JSON Output" --> S3OutputExtraction

    %% Classification Flow
    S3Classification -- "Triggers Event" --> SQSClassification
    SQSClassification -- "Consumed by" --> ClassificationWorker
    ClassificationWorker -- "Fetches Document" --> S3Classification
    ClassificationWorker -- "1. Text Detection" --> Textract
    Textract -- "Detected Text" --> ClassificationWorker
    ClassificationWorker -- "2. Classification" --> BedrockOpenAI
    BedrockOpenAI -- "Returns Classification" --> ClassificationWorker
    ClassificationWorker -- "Stores Classified PDFs" --> S3OutputClassification

    %% Styling
    classDef aws fill:#FF9900,stroke:#333,stroke-width:2px,color:#fff;
    class S3Input,S3Extraction,S3Classification,SQSExtraction,SQSClassification,S3OutputExtraction,S3OutputClassification,BedrockOpenAI,Textract,BedrockNova aws;

    classDef app fill:#264DE4,stroke:#333,stroke-width:2px,color:#fff;
    class FastAPI,ExtractionWorker,ClassificationWorker app;

    classDef monitoring fill:#9C27B0,stroke:#333,stroke-width:2px,color:#fff;
    class Langfuse monitoring;

```