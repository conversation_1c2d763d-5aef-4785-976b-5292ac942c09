{"classification_result": {"documents": [{"page_no": 1, "doc_type": "clear_to_pay"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 4.2752831177494954e-06, "Y": 0.0}, {"X": 1.0, "Y": 3.048375063485764e-08}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "3dbf5efa-2871-4995-a47f-160c5fe99f66", "Relationships": [{"Type": "CHILD", "Ids": ["5b87ec82-565a-4836-bd16-3b81b6144b21", "b375073d-b1f7-4b6d-9fcb-7cf8bbf77db1", "51f049a6-8df8-4951-8051-7d927ae10f92", "03a61e1b-8e81-41a2-a48b-a2bda63257be", "a1503304-7649-4868-9edd-1b5f0ffedd5d", "4640ad09-3f18-43b1-96e2-3ec7923c6bcc", "ea6ea6fe-ba0f-4151-8ac0-76b03c14edc0", "f35e1b8b-dbb7-4636-a191-fd13d8ca93ec", "45f63c19-63d6-489c-ac65-aa0c577b4316", "cd9bf9b3-3b2f-4523-a893-8c5590e60a3d", "94dee5da-b1a4-42a4-8984-4d42cc9ab4c5", "be5f2f44-1a63-4e5b-b5d0-5e9506e56fd2", "900b958c-5116-4956-b761-25f3db9365fe", "02459010-6c23-449d-9877-44016036f425", "d3e0271b-94e9-4914-bde8-695f9143550d", "08d58903-585c-44f5-9790-028aca417185", "2616858b-21b3-489a-8d21-b8e9eb161614", "8888f976-ea43-435c-aac6-29b76310ef4a", "f1515119-fcd7-496b-8389-8d045e52b2d7", "907a6aca-2cfc-4903-8400-a31da89ab954", "4e914165-c979-4260-9d94-ac40556a5fb8", "7fe9072b-a841-4cb3-b083-49eedf50c632", "5e73d0e0-dadb-4623-bd3b-6d5f117ff819", "cd67105b-6676-4ab0-ac12-591f02730952", "066f9ee2-1e9d-43fc-929a-cfc367502805", "052458fa-037d-4abf-bf79-c764bae7ae42", "fe07d393-a30c-4bee-972e-fa9fa3c734a9", "4d7d3eac-ef95-4118-af31-abbaff020d70", "d165c561-b72b-47f3-a40b-b4bc1ae95ea7", "889d6956-c1f3-41bc-a3b8-fe935216ade3", "6a42a39c-303b-4ba0-9a1e-0cd93182eff4", "713b72ec-dd5e-4cac-bea9-7cac2c852d9f", "0c0e9fc6-4709-4c74-85fa-7182de1f21b0", "b7a303b1-a8fb-4122-945e-98c6e3612f71"]}]}, {"BlockType": "LINE", "Confidence": 74.84810638427734, "Text": "<PERSON> Saw 8862654", "Geometry": {"BoundingBox": {"Width": 0.4815067946910858, "Height": 0.06642303615808487, "Left": 0.3618304431438446, "Top": 0.04821944236755371}, "Polygon": [{"X": 0.3618316948413849, "Y": 0.04821944236755371}, {"X": 0.8433372378349304, "Y": 0.04822885990142822}, {"X": 0.8433356285095215, "Y": 0.11464247852563858}, {"X": 0.3618304431438446, "Y": 0.11463332176208496}]}, "Id": "5b87ec82-565a-4836-bd16-3b81b6144b21", "Relationships": [{"Type": "CHILD", "Ids": ["7570b230-f2a9-4595-913e-fa75294fbddc", "e0faf3d8-1ae3-478e-b2a5-deb25c6258e7", "0dd855fe-b282-46a3-92b1-9f12e90c6e5a"]}]}, {"BlockType": "LINE", "Confidence": 20.487882614135742, "Text": "FMBVB", "Geometry": {"BoundingBox": {"Width": 0.22199513018131256, "Height": 0.033183567225933075, "Left": 0.40362420678138733, "Top": 0.13082414865493774}, "Polygon": [{"X": 0.40362483263015747, "Y": 0.13082414865493774}, {"X": 0.6256193518638611, "Y": 0.13082833588123322}, {"X": 0.6256186366081238, "Y": 0.16400770843029022}, {"X": 0.40362420678138733, "Y": 0.16400358080863953}]}, "Id": "b375073d-b1f7-4b6d-9fcb-7cf8bbf77db1", "Relationships": [{"Type": "CHILD", "Ids": ["a9985e04-b93c-4bd3-9110-2feca74021ee"]}]}, {"BlockType": "LINE", "Confidence": 92.90657043457031, "Text": "SAWYER NURSERY.", "Geometry": {"BoundingBox": {"Width": 0.4310744106769562, "Height": 0.02388864941895008, "Left": 0.2969056963920593, "Top": 0.17923475801944733}, "Polygon": [{"X": 0.29690614342689514, "Y": 0.17923475801944733}, {"X": 0.7279801368713379, "Y": 0.17924273014068604}, {"X": 0.7279796004295349, "Y": 0.20312340557575226}, {"X": 0.2969056963920593, "Y": 0.20311550796031952}]}, "Id": "51f049a6-8df8-4951-8051-7d927ae10f92", "Relationships": [{"Type": "CHILD", "Ids": ["a02def64-33ff-4e85-83a1-ccbaf46e2d19", "af85181e-a539-4bf7-9247-8c0fbfd18b59"]}]}, {"BlockType": "LINE", "Confidence": 99.93489074707031, "Text": "Truckload Check-in Form", "Geometry": {"BoundingBox": {"Width": 0.3436141610145569, "Height": 0.020961882546544075, "Left": 0.3491886854171753, "Top": 0.21136967837810516}, "Polygon": [{"X": 0.34918907284736633, "Y": 0.21136967837810516}, {"X": 0.6928028464317322, "Y": 0.21137595176696777}, {"X": 0.692802369594574, "Y": 0.23233157396316528}, {"X": 0.3491886854171753, "Y": 0.23232534527778625}]}, "Id": "03a61e1b-8e81-41a2-a48b-a2bda63257be", "Relationships": [{"Type": "CHILD", "Ids": ["1754b0de-d596-445b-aa40-80362964dd36", "28a1c272-f4b8-433f-b5ff-93fdca666c56", "b52a8d67-b4b1-4c21-8e6d-984b66c38ba9"]}]}, {"BlockType": "LINE", "Confidence": 99.84375, "Text": "Clear-to-Pay Voucher", "Geometry": {"BoundingBox": {"Width": 0.2910718023777008, "Height": 0.024676229804754257, "Left": 0.37657806277275085, "Top": 0.24060703814029694}, "Polygon": [{"X": 0.37657850980758667, "Y": 0.24060703814029694}, {"X": 0.6676498651504517, "Y": 0.24061228334903717}, {"X": 0.6676493287086487, "Y": 0.2652832567691803}, {"X": 0.37657806277275085, "Y": 0.26527807116508484}]}, "Id": "a1503304-7649-4868-9edd-1b5f0ffedd5d", "Relationships": [{"Type": "CHILD", "Ids": ["27d86605-c1e9-4bdd-9cfb-8e57665a99d2", "474982ee-6637-487f-a61b-d2d689bf5695"]}]}, {"BlockType": "LINE", "Confidence": 93.96514129638672, "Text": "Load # 20688 AC", "Geometry": {"BoundingBox": {"Width": 0.25071173906326294, "Height": 0.023287247866392136, "Left": 0.1133190393447876, "Top": 0.27356821298599243}, "Polygon": [{"X": 0.11331940442323685, "Y": 0.27356821298599243}, {"X": 0.36403077840805054, "Y": 0.2735726535320282}, {"X": 0.3640303313732147, "Y": 0.29685544967651367}, {"X": 0.1133190393447876, "Y": 0.2968510389328003}]}, "Id": "4640ad09-3f18-43b1-96e2-3ec7923c6bcc", "Relationships": [{"Type": "CHILD", "Ids": ["383ee763-f214-4c4f-9def-631c6369d819", "dba5100b-876e-4ce2-beb0-3b272b7845ec", "0e692448-487d-419b-a7d0-10d8938d2437", "4f5973ce-d7e4-4f82-8147-4dd3da89a64e"]}]}, {"BlockType": "LINE", "Confidence": 99.64285278320312, "Text": "Broker PE", "Geometry": {"BoundingBox": {"Width": 0.13300716876983643, "Height": 0.03143570199608803, "Left": 0.5809038281440735, "Top": 0.2671915888786316}, "Polygon": [{"X": 0.5809045433998108, "Y": 0.2671915888786316}, {"X": 0.7139109969139099, "Y": 0.2671939730644226}, {"X": 0.7139102816581726, "Y": 0.2986273169517517}, {"X": 0.5809038281440735, "Y": 0.2986249625682831}]}, "Id": "ea6ea6fe-ba0f-4151-8ac0-76b03c14edc0", "Relationships": [{"Type": "CHILD", "Ids": ["b0c72a75-a9a7-40ff-9048-229ca83535f1", "c3a86710-9ce6-43d8-aea0-f18dba6a97ab"]}]}, {"BlockType": "LINE", "Confidence": 97.16697692871094, "Text": "Date Processed 5/6/24", "Geometry": {"BoundingBox": {"Width": 0.2524978518486023, "Height": 0.030281124636530876, "Left": 0.11358046531677246, "Top": 0.3076259195804596}, "Polygon": [{"X": 0.11358094215393066, "Y": 0.3076259195804596}, {"X": 0.36607834696769714, "Y": 0.307630330324173}, {"X": 0.3660777509212494, "Y": 0.3379070460796356}, {"X": 0.11358046531677246, "Y": 0.337902694940567}]}, "Id": "f35e1b8b-dbb7-4636-a191-fd13d8ca93ec", "Relationships": [{"Type": "CHILD", "Ids": ["6941d573-8ba3-4ec5-953f-4647f816037a", "648c493d-10b2-4534-b5ba-5f1c99914d30", "583c21cb-75e5-49f3-9623-c5d8f215fe65"]}]}, {"BlockType": "LINE", "Confidence": 82.98509216308594, "Text": "Load Cost $3778.00", "Geometry": {"BoundingBox": {"Width": 0.23993289470672607, "Height": 0.02658865973353386, "Left": 0.5799322128295898, "Top": 0.3249439299106598}, "Polygon": [{"X": 0.5799327492713928, "Y": 0.3249439299106598}, {"X": 0.8198651075363159, "Y": 0.32494810223579407}, {"X": 0.8198644518852234, "Y": 0.35153260827064514}, {"X": 0.5799322128295898, "Y": 0.35152849555015564}]}, "Id": "45f63c19-63d6-489c-ac65-aa0c577b4316", "Relationships": [{"Type": "CHILD", "Ids": ["487629ec-d061-41cf-adf3-b0c383e4951e", "d6e9a934-e60d-405c-8d5b-09041007338a", "7e7b6a85-56b8-4dd6-8102-f40ea6a9d9ec"]}]}, {"BlockType": "LINE", "Confidence": 99.904541015625, "Text": "<PERSON><PERSON> Returned 38", "Geometry": {"BoundingBox": {"Width": 0.19116352498531342, "Height": 0.02133079059422016, "Left": 0.11803295463323593, "Top": 0.34394800662994385}, "Polygon": [{"X": 0.11803328990936279, "Y": 0.34394800662994385}, {"X": 0.30919647216796875, "Y": 0.3439512848854065}, {"X": 0.3091960847377777, "Y": 0.36527881026268005}, {"X": 0.11803295463323593, "Y": 0.3652755618095398}]}, "Id": "cd9bf9b3-3b2f-4523-a893-8c5590e60a3d", "Relationships": [{"Type": "CHILD", "Ids": ["47899cf5-c57f-4033-86e2-20468d15b05b", "840212ed-a74c-4f1b-9ba2-3ce2f5c02da3", "9783986a-12d8-4a87-934c-6ab854c92dd4"]}]}, {"BlockType": "LINE", "Confidence": 95.50874328613281, "Text": "Surcharge + $", "Geometry": {"BoundingBox": {"Width": 0.12006643414497375, "Height": 0.015037339180707932, "Left": 0.5743801593780518, "Top": 0.37245678901672363}, "Polygon": [{"X": 0.5743804574012756, "Y": 0.37245678901672363}, {"X": 0.6944465637207031, "Y": 0.372458815574646}, {"X": 0.6944462656974792, "Y": 0.38749411702156067}, {"X": 0.5743801593780518, "Y": 0.3874920904636383}]}, "Id": "94dee5da-b1a4-42a4-8984-4d42cc9ab4c5", "Relationships": [{"Type": "CHILD", "Ids": ["75a5a7ab-eaf8-465c-90d1-00350933a071", "8f664028-7ef8-46ce-9a6c-47e461b6da17", "83310e55-378a-4925-a940-f32f0c637fd5"]}]}, {"BlockType": "LINE", "Confidence": 92.41712951660156, "Text": "38 X $ 2.00 = $ 76.00", "Geometry": {"BoundingBox": {"Width": 0.3303971290588379, "Height": 0.02520992048084736, "Left": 0.12533865869045258, "Top": 0.4111941158771515}, "Polygon": [{"X": 0.1253390610218048, "Y": 0.4111941158771515}, {"X": 0.45573580265045166, "Y": 0.4111996293067932}, {"X": 0.45573529601097107, "Y": 0.4364040195941925}, {"X": 0.12533865869045258, "Y": 0.43639859557151794}]}, "Id": "be5f2f44-1a63-4e5b-b5d0-5e9506e56fd2", "Relationships": [{"Type": "CHILD", "Ids": ["17adef71-38b2-4826-b426-79357ffc6c47", "bb46fd03-0265-4e87-85c5-4fe005a3173c", "d7c92d83-cb4a-43ea-96dd-b7c6586490c2", "3cb06893-fc1f-479b-a08a-cf54370e2f16", "c95c284b-abc8-411f-bbdc-16b5889e4494"]}]}, {"BlockType": "LINE", "Confidence": 98.54143524169922, "Text": "Deductions - $", "Geometry": {"BoundingBox": {"Width": 0.12487909942865372, "Height": 0.012946837581694126, "Left": 0.5749068260192871, "Top": 0.4077560305595398}, "Polygon": [{"X": 0.5749070644378662, "Y": 0.4077560305595398}, {"X": 0.6997858881950378, "Y": 0.40775808691978455}, {"X": 0.699785590171814, "Y": 0.42070284485816956}, {"X": 0.5749068260192871, "Y": 0.4207007884979248}]}, "Id": "900b958c-5116-4956-b761-25f3db9365fe", "Relationships": [{"Type": "CHILD", "Ids": ["2a65cc71-b39f-4f29-b36a-1208dbf6bb54", "335b067c-d6c8-46db-935b-edf3b6af7f33"]}]}, {"BlockType": "LINE", "Confidence": 99.9802017211914, "Text": "RACKS AMOUNT TOTAL", "Geometry": {"BoundingBox": {"Width": 0.3170411288738251, "Height": 0.012996627017855644, "Left": 0.11322899907827377, "Top": 0.44025877118110657}, "Polygon": [{"X": 0.11322920024394989, "Y": 0.44025877118110657}, {"X": 0.43027013540267944, "Y": 0.4402639865875244}, {"X": 0.43026986718177795, "Y": 0.45325538516044617}, {"X": 0.11322899907827377, "Y": 0.4532502293586731}]}, "Id": "02459010-6c23-449d-9877-44016036f425", "Relationships": [{"Type": "CHILD", "Ids": ["6b74156a-846a-4c17-adbd-ea9afad40239", "1395ab12-edfe-4576-a1c5-9ecb330a198d", "dd967a10-2095-457e-9c7c-2c7a5ec71173"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.047779832035303116, "Height": 0.011783292517066002, "Left": 0.5780911445617676, "Top": 0.4598481357097626}, "Polygon": [{"X": 0.5780914425849915, "Y": 0.4598481357097626}, {"X": 0.6258710026741028, "Y": 0.45984891057014465}, {"X": 0.6258707642555237, "Y": 0.47163140773773193}, {"X": 0.5780911445617676, "Y": 0.47163063287734985}]}, "Id": "d3e0271b-94e9-4914-bde8-695f9143550d", "Relationships": [{"Type": "CHILD", "Ids": ["d18ee70d-f9f3-43d0-8cfe-313c29fc320a"]}]}, {"BlockType": "LINE", "Confidence": 91.42329406738281, "Text": "+ $ 76.00", "Geometry": {"BoundingBox": {"Width": 0.12221398949623108, "Height": 0.021547982469201088, "Left": 0.6805226802825928, "Top": 0.4513114094734192}, "Polygon": [{"X": 0.680523157119751, "Y": 0.4513114094734192}, {"X": 0.8027366399765015, "Y": 0.45131340622901917}, {"X": 0.8027361631393433, "Y": 0.4728594124317169}, {"X": 0.6805226802825928, "Y": 0.47285741567611694}]}, "Id": "08d58903-585c-44f5-9790-028aca417185", "Relationships": [{"Type": "CHILD", "Ids": ["c26294a5-d840-4dc8-8150-e5abe671f002", "45b0acf3-0edc-4e19-866b-2da0115cc451"]}]}, {"BlockType": "LINE", "Confidence": 99.96664428710938, "Text": "(Rack checks will now be sent to the", "Geometry": {"BoundingBox": {"Width": 0.2912226617336273, "Height": 0.013835422694683075, "Left": 0.5782169103622437, "Top": 0.476834237575531}, "Polygon": [{"X": 0.5782172083854675, "Y": 0.476834237575531}, {"X": 0.8694395422935486, "Y": 0.47683894634246826}, {"X": 0.8694391846656799, "Y": 0.4906696677207947}, {"X": 0.5782169103622437, "Y": 0.4906650185585022}]}, "Id": "2616858b-21b3-489a-8d21-b8e9eb161614", "Relationships": [{"Type": "CHILD", "Ids": ["bc3ba64b-7c67-40d3-85de-3afdb03ea821", "d49138e9-4133-4c58-9d86-9486b1a8e546", "333c1450-c96a-40e5-a20b-0abf4f2e0106", "afb2a8ca-3643-4370-8fec-25fb50b52dee", "1f6e4e24-4ef0-46ae-ba92-8dc2c1b45179", "8a4fe8ea-3277-4e46-8e0e-dcf97c12614c", "8d9ab00d-7652-4a39-8176-adc98e2fb7ab", "1700a1be-44e1-4b6f-bef3-736aab750597"]}]}, {"BlockType": "LINE", "Confidence": 99.56233978271484, "Text": "broker.)", "Geometry": {"BoundingBox": {"Width": 0.06317983567714691, "Height": 0.014271195977926254, "Left": 0.5883939862251282, "Top": 0.4937463700771332}, "Polygon": [{"X": 0.588394284248352, "Y": 0.4937463700771332}, {"X": 0.6515738368034363, "Y": 0.49374738335609436}, {"X": 0.6515734791755676, "Y": 0.5080175399780273}, {"X": 0.5883939862251282, "Y": 0.5080165266990662}]}, "Id": "8888f976-ea43-435c-aac6-29b76310ef4a", "Relationships": [{"Type": "CHILD", "Ids": ["dbaae6d0-5f44-41d7-9dcf-b439c9343fde"]}]}, {"BlockType": "LINE", "Confidence": 98.29400634765625, "Text": "Net Cost $ 3854.00", "Geometry": {"BoundingBox": {"Width": 0.21267032623291016, "Height": 0.03089769557118416, "Left": 0.5789120197296143, "Top": 0.5123988389968872}, "Polygon": [{"X": 0.5789126753807068, "Y": 0.5123988389968872}, {"X": 0.7915823459625244, "Y": 0.5124021768569946}, {"X": 0.7915816307067871, "Y": 0.5432965159416199}, {"X": 0.5789120197296143, "Y": 0.5432931780815125}]}, "Id": "f1515119-fcd7-496b-8389-8d045e52b2d7", "Relationships": [{"Type": "CHILD", "Ids": ["5937b13a-9cac-44d8-831d-b034d73167fd", "27aa7e6a-9de2-4158-8e62-37d30c136292", "07da3d07-74ee-43ff-9c9f-6c9c77b7b788"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Billable Shortages:", "Geometry": {"BoundingBox": {"Width": 0.2505904734134674, "Height": 0.02372152730822563, "Left": 0.11305772513151169, "Top": 0.5637322068214417}, "Polygon": [{"X": 0.11305809766054153, "Y": 0.5637322068214417}, {"X": 0.3636482059955597, "Y": 0.563736081123352}, {"X": 0.3636477589607239, "Y": 0.5874537229537964}, {"X": 0.11305772513151169, "Y": 0.5874499082565308}]}, "Id": "907a6aca-2cfc-4903-8400-a31da89ab954", "Relationships": [{"Type": "CHILD", "Ids": ["d36e809a-f44f-481e-9ea8-509eb0256e6b", "55803698-8d7f-461c-9de3-b15c290b808f"]}]}, {"BlockType": "LINE", "Confidence": 93.5877685546875, "Text": "(equipment missing with insufficient documentation)", "Geometry": {"BoundingBox": {"Width": 0.28322431445121765, "Height": 0.010407715104520321, "Left": 0.11157942563295364, "Top": 0.5890485048294067}, "Polygon": [{"X": 0.11157958954572678, "Y": 0.5890485048294067}, {"X": 0.3948037326335907, "Y": 0.5890527963638306}, {"X": 0.39480355381965637, "Y": 0.5994561910629272}, {"X": 0.11157942563295364, "Y": 0.5994518995285034}]}, "Id": "4e914165-c979-4260-9d94-ac40556a5fb8", "Relationships": [{"Type": "CHILD", "Ids": ["6141deea-6d71-4314-b6b2-736399abf17d", "60f72ee6-9b42-4acd-b8eb-3f0622a1b4a0", "441dbcbf-fd26-44be-baeb-fc02db175c56", "4abc8248-1354-4e27-a4e6-da336b3f1578", "74860097-1ac6-4795-a32f-92ff48680aa6"]}]}, {"BlockType": "LINE", "Confidence": 96.08216094970703, "Text": "Racks: ( ) X $250.00 = $", "Geometry": {"BoundingBox": {"Width": 0.2016485184431076, "Height": 0.01452554203569889, "Left": 0.11255775392055511, "Top": 0.6127409338951111}, "Polygon": [{"X": 0.11255797743797302, "Y": 0.6127409338951111}, {"X": 0.3142062723636627, "Y": 0.6127439737319946}, {"X": 0.31420600414276123, "Y": 0.6272664666175842}, {"X": 0.11255775392055511, "Y": 0.6272634863853455}]}, "Id": "7fe9072b-a841-4cb3-b083-49eedf50c632", "Relationships": [{"Type": "CHILD", "Ids": ["6351e087-53be-47c9-8fbb-42b4e0ed9b0a", "abf8e47f-9df7-45fa-8764-326f315bebdb", "6c9cec6e-cd37-4500-a648-63059bcdaeb2", "ff726279-acd0-474a-b9ba-b053f7d12128", "38ff40f6-3d2f-4947-b947-f765e8555cec", "212d8090-15d1-4ae1-a92c-c15155b34cea", "c244ec46-c52f-4e92-8d2f-69d1ec00489e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Deductions", "Geometry": {"BoundingBox": {"Width": 0.13835491240024567, "Height": 0.012028276920318604, "Left": 0.5969895720481873, "Top": 0.614647626876831}, "Polygon": [{"X": 0.5969898700714111, "Y": 0.614647626876831}, {"X": 0.7353445291519165, "Y": 0.6146497130393982}, {"X": 0.7353442311286926, "Y": 0.6266759037971497}, {"X": 0.5969895720481873, "Y": 0.6266738772392273}]}, "Id": "5e73d0e0-dadb-4623-bd3b-6d5f117ff819", "Relationships": [{"Type": "CHILD", "Ids": ["9e0ce7e2-e249-42ae-a924-9291c5ae5dbc", "61f41e4c-1984-4c14-9c62-9f31701396e7"]}]}, {"BlockType": "LINE", "Confidence": 99.94061279296875, "Text": "For Load:", "Geometry": {"BoundingBox": {"Width": 0.07692092657089233, "Height": 0.011965513229370117, "Left": 0.5990846157073975, "Top": 0.6325882077217102}, "Polygon": [{"X": 0.5990849137306213, "Y": 0.6325882077217102}, {"X": 0.6760055422782898, "Y": 0.6325893402099609}, {"X": 0.6760053038597107, "Y": 0.6445537209510803}, {"X": 0.5990846157073975, "Y": 0.6445525884628296}]}, "Id": "cd67105b-6676-4ab0-ac12-591f02730952", "Relationships": [{"Type": "CHILD", "Ids": ["f3e53045-b4b2-4f55-bd22-356ca3662cef", "b26b38e5-8b24-4191-b1f1-8417ddf27f9a"]}]}, {"BlockType": "LINE", "Confidence": 96.1303482055664, "Text": "Load Locks: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.23684898018836975, "Height": 0.014523995108902454, "Left": 0.1130131408572197, "Top": 0.6478518843650818}, "Polygon": [{"X": 0.1130133643746376, "Y": 0.6478518843650818}, {"X": 0.34986212849617004, "Y": 0.6478553414344788}, {"X": 0.34986186027526855, "Y": 0.6623758673667908}, {"X": 0.1130131408572197, "Y": 0.6623724102973938}]}, "Id": "066f9ee2-1e9d-43fc-929a-cfc367502805", "Relationships": [{"Type": "CHILD", "Ids": ["0f494b0b-3a26-4656-813c-8274c710eab1", "968d293c-998f-478e-87ae-328ceb6109b4", "333286eb-b9ae-405d-9920-7d04cc0fe5ff", "1a2c2326-e8b8-40f6-9886-b1ac53a7cec7", "14bf7af6-82ad-465d-8e34-763f8f699cf0", "b542a72e-7541-473e-b2ba-99c82d3ffe25", "fc1cf4ce-6fac-4de8-b25a-f4d68360b4cb", "d2659b54-ee7d-4735-92f2-34feffc4efab"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "$", "Geometry": {"BoundingBox": {"Width": 0.009334661066532135, "Height": 0.01311455387622118, "Left": 0.598357617855072, "Top": 0.6671093106269836}, "Polygon": [{"X": 0.5983579158782959, "Y": 0.6671093106269836}, {"X": 0.607692301273346, "Y": 0.6671094298362732}, {"X": 0.6076920032501221, "Y": 0.680223822593689}, {"X": 0.598357617855072, "Y": 0.6802237033843994}]}, "Id": "052458fa-037d-4abf-bf79-c764bae7ae42", "Relationships": [{"Type": "CHILD", "Ids": ["ccd80a6a-48df-4a65-a5d6-a819f3a4bea3"]}]}, {"BlockType": "LINE", "Confidence": 95.49401092529297, "Text": "Pallet Jack: ( ) X $500.00 = $", "Geometry": {"BoundingBox": {"Width": 0.2377030849456787, "Height": 0.01464083045721054, "Left": 0.11303280293941498, "Top": 0.6828960180282593}, "Polygon": [{"X": 0.11303302645683289, "Y": 0.6828960180282593}, {"X": 0.3507359027862549, "Y": 0.6828994750976562}, {"X": 0.350735604763031, "Y": 0.697536826133728}, {"X": 0.11303280293941498, "Y": 0.6975334286689758}]}, "Id": "fe07d393-a30c-4bee-972e-fa9fa3c734a9", "Relationships": [{"Type": "CHILD", "Ids": ["a4c03ac6-8ae0-40e2-9fcc-5fc87fc125a4", "5efc41f8-283a-4c3c-a95c-9af2f1c1dd0e", "d9e6d6f7-2e5d-4476-8a06-6bce13c0eb88", "0d93b033-6e7c-4dd0-95d9-8e0743765143", "f686a88d-887a-43ae-bf1e-8dbc0a1d927e", "8d81deba-30b1-4307-8dd2-e7010deed7de", "0e6d24bf-6afd-4220-aa44-8680f76dc83a"]}]}, {"BlockType": "LINE", "Confidence": 96.29546356201172, "Text": "Load Straps: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.23813863098621368, "Height": 0.014955483376979828, "Left": 0.11297637969255447, "Top": 0.7173449993133545}, "Polygon": [{"X": 0.11297661811113358, "Y": 0.7173449993133545}, {"X": 0.35111501812934875, "Y": 0.7173483967781067}, {"X": 0.3511147201061249, "Y": 0.7323004603385925}, {"X": 0.11297637969255447, "Y": 0.7322971224784851}]}, "Id": "4d7d3eac-ef95-4118-af31-abbaff020d70", "Relationships": [{"Type": "CHILD", "Ids": ["421b03ef-4509-4fd1-a552-4dc3601cb595", "e4bee59c-7b13-4816-9f04-3c4bc9228276", "85279db9-6de8-4e81-b7d5-dffe11bc777d", "b5a6feac-2645-4a4a-a349-06eb6bcd3011", "69ba994e-546a-4327-9595-ef12c93efe34", "543fc47a-894d-471b-971d-9ac3d3cdb3fa", "f95793ca-8801-460e-b5ea-dd39d669f73d", "98e9b7fb-f66b-4ce1-a4be-7352d317ce17"]}]}, {"BlockType": "LINE", "Confidence": 99.94905853271484, "Text": "Paperwork & other Fines $", "Geometry": {"BoundingBox": {"Width": 0.21409283578395844, "Height": 0.014971680007874966, "Left": 0.11243946850299835, "Top": 0.7521683573722839}, "Polygon": [{"X": 0.11243970692157745, "Y": 0.7521683573722839}, {"X": 0.3265323042869568, "Y": 0.7521713376045227}, {"X": 0.3265320360660553, "Y": 0.7671400308609009}, {"X": 0.11243946850299835, "Y": 0.7671370506286621}]}, "Id": "d165c561-b72b-47f3-a40b-b4bc1ae95ea7", "Relationships": [{"Type": "CHILD", "Ids": ["cc494057-95c3-4c4a-8651-7012602c8b63", "fdb8ebdf-ee33-47fa-b711-aa40cadf6dc1", "6d658272-47d8-4f37-af0c-06d72c4ae05f", "3575ef6b-4119-4f6f-9e83-957f8825df3d", "4a579981-4583-463a-835c-d7f6027bf8c1"]}]}, {"BlockType": "LINE", "Confidence": 99.38257598876953, "Text": "(see attached documentation)", "Geometry": {"BoundingBox": {"Width": 0.23234796524047852, "Height": 0.015056069009006023, "Left": 0.1120402067899704, "Top": 0.7696284651756287}, "Polygon": [{"X": 0.1120404377579689, "Y": 0.7696284651756287}, {"X": 0.3443881571292877, "Y": 0.7696316838264465}, {"X": 0.34438788890838623, "Y": 0.7846845388412476}, {"X": 0.1120402067899704, "Y": 0.7846813797950745}]}, "Id": "889d6956-c1f3-41bc-a3b8-fe935216ade3", "Relationships": [{"Type": "CHILD", "Ids": ["f4639c08-1c11-496b-b4c6-9a95a520b790", "fffc2345-28ea-4b41-a5f8-2711e2db99ec", "45b58d23-dc2b-4ded-9e08-43ee081fc7c8"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Manager A<PERSON><PERSON><PERSON>:", "Geometry": {"BoundingBox": {"Width": 0.15403610467910767, "Height": 0.015256835147738457, "Left": 0.5434603691101074, "Top": 0.7714486718177795}, "Polygon": [{"X": 0.5434606671333313, "Y": 0.7714486718177795}, {"X": 0.6974964737892151, "Y": 0.7714508175849915}, {"X": 0.6974961161613464, "Y": 0.7867055535316467}, {"X": 0.5434603691101074, "Y": 0.7867034077644348}]}, "Id": "6a42a39c-303b-4ba0-9a1e-0cd93182eff4", "Relationships": [{"Type": "CHILD", "Ids": ["674e0b93-2c3b-427e-b46b-b2c442b21853", "286f44ac-6c1d-40a5-911e-cdcd218f09d8"]}]}, {"BlockType": "LINE", "Confidence": 77.2520751953125, "Text": "my", "Geometry": {"BoundingBox": {"Width": 0.0913793221116066, "Height": 0.0678924173116684, "Left": 0.7808471322059631, "Top": 0.7411839365959167}, "Polygon": [{"X": 0.7808487415313721, "Y": 0.7411839365959167}, {"X": 0.8722264766693115, "Y": 0.741185188293457}, {"X": 0.872224748134613, "Y": 0.8090763092041016}, {"X": 0.7808471322059631, "Y": 0.809075117111206}]}, "Id": "713b72ec-dd5e-4cac-bea9-7cac2c852d9f", "Relationships": [{"Type": "CHILD", "Ids": ["ca53f360-3e31-46c2-99dd-20f0508dcb1c"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Notes:", "Geometry": {"BoundingBox": {"Width": 0.05062611773610115, "Height": 0.01179698295891285, "Left": 0.1118214800953865, "Top": 0.8050987124443054}, "Polygon": [{"X": 0.11182166635990143, "Y": 0.8050987124443054}, {"X": 0.16244760155677795, "Y": 0.8050994277000427}, {"X": 0.16244740784168243, "Y": 0.8168957233428955}, {"X": 0.1118214800953865, "Y": 0.8168950080871582}]}, "Id": "0c0e9fc6-4709-4c74-85fa-7182de1f21b0", "Relationships": [{"Type": "CHILD", "Ids": ["6854a02f-3096-4765-912d-cc55aaa5c634"]}]}, {"BlockType": "LINE", "Confidence": 99.14944458007812, "Text": "Any fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616) 669-7468", "Geometry": {"BoundingBox": {"Width": 0.7662862539291382, "Height": 0.014987267553806305, "Left": 0.11139937490224838, "Top": 0.8560072183609009}, "Polygon": [{"X": 0.11139960587024689, "Y": 0.8560072183609009}, {"X": 0.8776856660842896, "Y": 0.8560172319412231}, {"X": 0.8776852488517761, "Y": 0.8709944486618042}, {"X": 0.11139937490224838, "Y": 0.8709844946861267}]}, "Id": "b7a303b1-a8fb-4122-945e-98c6e3612f71", "Relationships": [{"Type": "CHILD", "Ids": ["ab515ac2-7be9-4cfe-9518-6cd295de925a", "dfe2d583-62f6-47bc-a593-18bd175a18e3", "6e0d9a2c-f5be-4d04-be4f-3b275004fc06", "c2cc48e9-31e9-4a8a-a940-c121bcb6993c", "48e51092-0004-4c7f-8deb-9cf5f56346d0", "eaaa0e8e-110b-419c-8e1f-bcebfc7f083a", "02ce8791-a72f-45f1-b99e-a3ed935b89bd", "ffb4c098-6ef6-4748-82d2-33ad3ee8ac02", "f59c603b-86ef-4867-9ac5-55d2883c74ad", "07083b70-3311-4c0d-83d9-d840927b12c1", "c6bd1fd6-e707-4c6d-be23-6b0281a58e09", "7fa23193-9985-4cb0-b1e8-2acb42161d9d", "9b83f0c9-3a41-45ff-af5b-804f56dc9b44", "25bf2115-a7ef-41d2-8dcc-b2452fd5fe08", "c4461a95-771b-4770-8c58-9f5ff22c9209", "40c0af22-6e0c-4a2d-a5b5-bc4671e400b2", "87f0ba7b-4c69-43f8-90b6-ecbd00869935", "a66e82af-674d-44ac-ba68-b79edb6b5e37", "438f569f-1a59-4507-928b-3def3913da29", "0ae8bbd3-38bd-41e4-886d-9c21cd8bd88c", "d0276b9a-7576-4035-9e1b-b7247f5cc49b"]}]}, {"BlockType": "WORD", "Confidence": 26.051998138427734, "Text": "Lone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05357526242733002, "Height": 0.015265841968357563, "Left": 0.3618306815624237, "Top": 0.0876234620809555}, "Polygon": [{"X": 0.3618309497833252, "Y": 0.0876234620809555}, {"X": 0.41540592908859253, "Y": 0.08762449026107788}, {"X": 0.41540563106536865, "Y": 0.1028892993927002}, {"X": 0.3618306815624237, "Y": 0.10288827866315842}]}, "Id": "7570b230-f2a9-4595-913e-fa75294fbddc"}, {"BlockType": "WORD", "Confidence": 98.96265411376953, "Text": "<PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.16218321025371552, "Height": 0.058795344084501266, "Left": 0.40340325236320496, "Top": 0.048220254480838776}, "Polygon": [{"X": 0.4034043848514557, "Y": 0.048220254480838776}, {"X": 0.5655864477157593, "Y": 0.048223428428173065}, {"X": 0.565585196018219, "Y": 0.10701560229063034}, {"X": 0.40340325236320496, "Y": 0.10701250284910202}]}, "Id": "e0faf3d8-1ae3-478e-b2a5-deb25c6258e7"}, {"BlockType": "WORD", "Confidence": 99.52965545654297, "Text": "8862654", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.2598079442977905, "Height": 0.056145478039979935, "Left": 0.5835290551185608, "Top": 0.058497004210948944}, "Polygon": [{"X": 0.5835302472114563, "Y": 0.058497004210948944}, {"X": 0.8433369994163513, "Y": 0.058502063155174255}, {"X": 0.8433356285095215, "Y": 0.11464247852563858}, {"X": 0.5835290551185608, "Y": 0.11463753879070282}]}, "Id": "0dd855fe-b282-46a3-92b1-9f12e90c6e5a"}, {"BlockType": "WORD", "Confidence": 20.487882614135742, "Text": "FMBVB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22199513018131256, "Height": 0.033183567225933075, "Left": 0.40362420678138733, "Top": 0.13082414865493774}, "Polygon": [{"X": 0.40362483263015747, "Y": 0.13082414865493774}, {"X": 0.6256193518638611, "Y": 0.13082833588123322}, {"X": 0.6256186366081238, "Y": 0.16400770843029022}, {"X": 0.40362420678138733, "Y": 0.16400358080863953}]}, "Id": "a9985e04-b93c-4bd3-9110-2feca74021ee"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "SAWYER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18650655448436737, "Height": 0.0227418951690197, "Left": 0.2969057261943817, "Top": 0.17923475801944733}, "Polygon": [{"X": 0.29690614342689514, "Y": 0.17923475801944733}, {"X": 0.4834122657775879, "Y": 0.1792382150888443}, {"X": 0.4834118187427521, "Y": 0.20197665691375732}, {"X": 0.2969057261943817, "Y": 0.20197322964668274}]}, "Id": "a02def64-33ff-4e85-83a1-ccbaf46e2d19"}, {"BlockType": "WORD", "Confidence": 85.90103149414062, "Text": "NURSERY.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21921566128730774, "Height": 0.022625915706157684, "Left": 0.5087644457817078, "Top": 0.18049749732017517}, "Polygon": [{"X": 0.508764922618866, "Y": 0.18049749732017517}, {"X": 0.7279800772666931, "Y": 0.1805015504360199}, {"X": 0.7279796004295349, "Y": 0.20312340557575226}, {"X": 0.5087644457817078, "Y": 0.2031193971633911}]}, "Id": "af85181e-a539-4bf7-9247-8c0fbfd18b59"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truckload", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13454456627368927, "Height": 0.019167466089129448, "Left": 0.3491887152194977, "Top": 0.21136967837810516}, "Polygon": [{"X": 0.34918907284736633, "Y": 0.21136967837810516}, {"X": 0.48373326659202576, "Y": 0.21137213706970215}, {"X": 0.4837329089641571, "Y": 0.23053714632987976}, {"X": 0.3491887152194977, "Y": 0.23053471744060516}]}, "Id": "1754b0de-d596-445b-aa40-80362964dd36"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "Check-in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11983013898134232, "Height": 0.01930137909948826, "Left": 0.4939562976360321, "Top": 0.21243509650230408}, "Polygon": [{"X": 0.49395668506622314, "Y": 0.21243509650230408}, {"X": 0.6137864589691162, "Y": 0.21243728697299957}, {"X": 0.6137860417366028, "Y": 0.23173648118972778}, {"X": 0.4939562976360321, "Y": 0.23173432052135468}]}, "Id": "28a1c272-f4b8-433f-b5ff-93fdca666c56"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Form", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06970486789941788, "Height": 0.01870773173868656, "Left": 0.6230979561805725, "Top": 0.21362383663654327}, "Polygon": [{"X": 0.6230983734130859, "Y": 0.21362383663654327}, {"X": 0.6928027868270874, "Y": 0.21362510323524475}, {"X": 0.692802369594574, "Y": 0.23233157396316528}, {"X": 0.6230979561805725, "Y": 0.2323303073644638}]}, "Id": "b52a8d67-b4b1-4c21-8e6d-984b66c38ba9"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "Clear-to-Pay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16823478043079376, "Height": 0.02467404119670391, "Left": 0.37657806277275085, "Top": 0.24060703814029694}, "Polygon": [{"X": 0.37657850980758667, "Y": 0.24060703814029694}, {"X": 0.5448128581047058, "Y": 0.24061007797718048}, {"X": 0.5448123216629028, "Y": 0.265281081199646}, {"X": 0.37657806277275085, "Y": 0.26527807116508484}]}, "Id": "27d86605-c1e9-4bdd-9cfb-8e57665a99d2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Voucher", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1138218492269516, "Height": 0.01973279006779194, "Left": 0.5538280010223389, "Top": 0.24182388186454773}, "Polygon": [{"X": 0.5538284182548523, "Y": 0.24182388186454773}, {"X": 0.6676498055458069, "Y": 0.24182593822479248}, {"X": 0.6676493883132935, "Y": 0.2615566849708557}, {"X": 0.5538280010223389, "Y": 0.26155462861061096}]}, "Id": "474982ee-6637-487f-a61b-d2d689bf5695"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039636824280023575, "Height": 0.012018379755318165, "Left": 0.11331905424594879, "Top": 0.28371724486351013}, "Polygon": [{"X": 0.11331924051046371, "Y": 0.28371724486351013}, {"X": 0.15295587480068207, "Y": 0.28371796011924744}, {"X": 0.15295568108558655, "Y": 0.295735627412796}, {"X": 0.11331905424594879, "Y": 0.2957349419593811}]}, "Id": "383ee763-f214-4c4f-9def-631c6369d819"}, {"BlockType": "WORD", "Confidence": 99.375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010338881984353065, "Height": 0.01198855321854353, "Left": 0.15816551446914673, "Top": 0.2840760052204132}, "Polygon": [{"X": 0.15816572308540344, "Y": 0.2840760052204132}, {"X": 0.16850440204143524, "Y": 0.28407618403434753}, {"X": 0.16850420832633972, "Y": 0.296064555644989}, {"X": 0.15816551446914673, "Y": 0.2960643768310547}]}, "Id": "dba5100b-876e-4ce2-beb0-3b272b7845ec"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "20688", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09907423704862595, "Height": 0.02293766476213932, "Left": 0.18774785101413727, "Top": 0.2735695242881775}, "Polygon": [{"X": 0.1877482384443283, "Y": 0.2735695242881775}, {"X": 0.2868220806121826, "Y": 0.27357128262519836}, {"X": 0.2868216931819916, "Y": 0.29650720953941345}, {"X": 0.18774785101413727, "Y": 0.2965054512023926}]}, "Id": "0e692448-487d-419b-a7d0-10d8938d2437"}, {"BlockType": "WORD", "Confidence": 76.71097564697266, "Text": "AC", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.04490329325199127, "Height": 0.01964282989501953, "Left": 0.3191274106502533, "Top": 0.27721261978149414}, "Polygon": [{"X": 0.31912776827812195, "Y": 0.27721261978149414}, {"X": 0.3640306890010834, "Y": 0.2772134244441986}, {"X": 0.3640303313732147, "Y": 0.29685544967651367}, {"X": 0.3191274106502533, "Y": 0.2968546748161316}]}, "Id": "4f5973ce-d7e4-4f82-8147-4dd3da89a64e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Broker", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05502450093626976, "Height": 0.01212046667933464, "Left": 0.5809038281440735, "Top": 0.28650546073913574}, "Polygon": [{"X": 0.5809041261672974, "Y": 0.28650546073913574}, {"X": 0.6359283328056335, "Y": 0.28650644421577454}, {"X": 0.6359280943870544, "Y": 0.2986259460449219}, {"X": 0.5809038281440735, "Y": 0.2986249625682831}]}, "Id": "b0c72a75-a9a7-40ff-9048-229ca83535f1"}, {"BlockType": "WORD", "Confidence": 99.28571319580078, "Text": "PE", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.041716318577528, "Height": 0.024488165974617004, "Left": 0.6721947193145752, "Top": 0.2671932280063629}, "Polygon": [{"X": 0.6721952557563782, "Y": 0.2671932280063629}, {"X": 0.7139109969139099, "Y": 0.2671939730644226}, {"X": 0.7139104604721069, "Y": 0.2916813790798187}, {"X": 0.6721947193145752, "Y": 0.2916806638240814}]}, "Id": "c3a86710-9ce6-43d8-aea0-f18dba6a97ab"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037055522203445435, "Height": 0.011652990244328976, "Left": 0.11358059197664261, "Top": 0.3182178735733032}, "Polygon": [{"X": 0.11358077824115753, "Y": 0.3182178735733032}, {"X": 0.15063612163066864, "Y": 0.31821852922439575}, {"X": 0.15063592791557312, "Y": 0.32987087965011597}, {"X": 0.11358059197664261, "Y": 0.32987022399902344}]}, "Id": "6941d573-8ba3-4ec5-953f-4647f816037a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Processed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07884678244590759, "Height": 0.011946904473006725, "Left": 0.15527381002902985, "Top": 0.3183774948120117}, "Polygon": [{"X": 0.15527400374412537, "Y": 0.3183774948120117}, {"X": 0.23412059247493744, "Y": 0.31837886571884155}, {"X": 0.23412038385868073, "Y": 0.3303244113922119}, {"X": 0.15527381002902985, "Y": 0.3303230404853821}]}, "Id": "648c493d-10b2-4534-b5ba-5f1c99914d30"}, {"BlockType": "WORD", "Confidence": 91.50091552734375, "Text": "5/6/24", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.11554791033267975, "Height": 0.030278729274868965, "Left": 0.2505304217338562, "Top": 0.3076283037662506}, "Polygon": [{"X": 0.2505309581756592, "Y": 0.3076283037662506}, {"X": 0.36607834696769714, "Y": 0.307630330324173}, {"X": 0.3660777509212494, "Y": 0.3379070460796356}, {"X": 0.2505304217338562, "Y": 0.33790504932403564}]}, "Id": "583c21cb-75e5-49f3-9623-c5d8f215fe65"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039706919342279434, "Height": 0.01170013751834631, "Left": 0.5799322724342346, "Top": 0.33798980712890625}, "Polygon": [{"X": 0.5799325108528137, "Y": 0.33798980712890625}, {"X": 0.6196391582489014, "Y": 0.33799049258232117}, {"X": 0.6196389198303223, "Y": 0.34968993067741394}, {"X": 0.5799322724342346, "Y": 0.349689245223999}]}, "Id": "487629ec-d061-41cf-adf3-b0c383e4951e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035990022122859955, "Height": 0.011347324587404728, "Left": 0.625203549861908, "Top": 0.33853089809417725}, "Polygon": [{"X": 0.6252037882804871, "Y": 0.33853089809417725}, {"X": 0.6611935496330261, "Y": 0.3385315239429474}, {"X": 0.661193311214447, "Y": 0.3498782217502594}, {"X": 0.625203549861908, "Y": 0.34987759590148926}]}, "Id": "d6e9a934-e60d-405c-8d5b-09041007338a"}, {"BlockType": "WORD", "Confidence": 49.01387023925781, "Text": "$3778.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1292736530303955, "Height": 0.026586739346385002, "Left": 0.6905914545059204, "Top": 0.324945867061615}, "Polygon": [{"X": 0.6905920505523682, "Y": 0.324945867061615}, {"X": 0.8198651075363159, "Y": 0.32494810223579407}, {"X": 0.8198644518852234, "Y": 0.35153260827064514}, {"X": 0.6905914545059204, "Y": 0.35153037309646606}]}, "Id": "7e7b6a85-56b8-4dd6-8102-f40ea6a9d9ec"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048180002719163895, "Height": 0.011579036712646484, "Left": 0.11803297698497772, "Top": 0.35245344042778015}, "Polygon": [{"X": 0.11803315579891205, "Y": 0.35245344042778015}, {"X": 0.16621297597885132, "Y": 0.352454274892807}, {"X": 0.1662127822637558, "Y": 0.36403247714042664}, {"X": 0.11803297698497772, "Y": 0.3640316426753998}]}, "Id": "47899cf5-c57f-4033-86e2-20468d15b05b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Returned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07249883562326431, "Height": 0.011482935398817062, "Left": 0.17112356424331665, "Top": 0.35286054015159607}, "Polygon": [{"X": 0.17112374305725098, "Y": 0.35286054015159607}, {"X": 0.24362239241600037, "Y": 0.35286179184913635}, {"X": 0.24362219870090485, "Y": 0.36434346437454224}, {"X": 0.17112356424331665, "Y": 0.36434224247932434}]}, "Id": "840212ed-a74c-4f1b-9ba2-3ce2f5c02da3"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "38", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03666705638170242, "Height": 0.021328134462237358, "Left": 0.2725294232368469, "Top": 0.34395065903663635}, "Polygon": [{"X": 0.27252981066703796, "Y": 0.34395065903663635}, {"X": 0.30919647216796875, "Y": 0.3439512848854065}, {"X": 0.3091960847377777, "Y": 0.36527881026268005}, {"X": 0.2725294232368469, "Y": 0.3652781844139099}]}, "Id": "9783986a-12d8-4a87-934c-6ab854c92dd4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Surcharge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08016806095838547, "Height": 0.01503666676580906, "Left": 0.5743801593780518, "Top": 0.37245678901672363}, "Polygon": [{"X": 0.5743804574012756, "Y": 0.37245678901672363}, {"X": 0.6545482277870178, "Y": 0.3724581301212311}, {"X": 0.6545478701591492, "Y": 0.38749346137046814}, {"X": 0.5743801593780518, "Y": 0.3874920904636383}]}, "Id": "75a5a7ab-eaf8-465c-90d1-00350933a071"}, {"BlockType": "WORD", "Confidence": 98.34263610839844, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009469850920140743, "Height": 0.007920337840914726, "Left": 0.6649258732795715, "Top": 0.37499114871025085}, "Polygon": [{"X": 0.6649260520935059, "Y": 0.37499114871025085}, {"X": 0.674395740032196, "Y": 0.3749912977218628}, {"X": 0.6743955612182617, "Y": 0.38291147351264954}, {"X": 0.6649258732795715, "Y": 0.3829113245010376}]}, "Id": "8f664028-7ef8-46ce-9a6c-47e461b6da17"}, {"BlockType": "WORD", "Confidence": 88.212890625, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009416043758392334, "Height": 0.012409133836627007, "Left": 0.6850305199623108, "Top": 0.3728368878364563}, "Polygon": [{"X": 0.6850308179855347, "Y": 0.3728368878364563}, {"X": 0.6944465637207031, "Y": 0.37283703684806824}, {"X": 0.694446325302124, "Y": 0.38524600863456726}, {"X": 0.6850305199623108, "Y": 0.3852458596229553}]}, "Id": "83310e55-378a-4925-a940-f32f0c637fd5"}, {"BlockType": "WORD", "Confidence": 99.22772216796875, "Text": "38", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0346793569624424, "Height": 0.024383392184972763, "Left": 0.12533867359161377, "Top": 0.4111941158771515}, "Polygon": [{"X": 0.1253390610218048, "Y": 0.4111941158771515}, {"X": 0.16001802682876587, "Y": 0.41119468212127686}, {"X": 0.16001762449741364, "Y": 0.43557751178741455}, {"X": 0.12533867359161377, "Y": 0.4355769455432892}]}, "Id": "17adef71-38b2-4826-b426-79357ffc6c47"}, {"BlockType": "WORD", "Confidence": 81.14018249511719, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014188213273882866, "Height": 0.011887572705745697, "Left": 0.19708511233329773, "Top": 0.42290714383125305}, "Polygon": [{"X": 0.19708530604839325, "Y": 0.42290714383125305}, {"X": 0.21127332746982574, "Y": 0.42290738224983215}, {"X": 0.21127311885356903, "Y": 0.43479469418525696}, {"X": 0.19708511233329773, "Y": 0.43479448556900024}]}, "Id": "bb46fd03-0265-4e87-85c5-4fe005a3173c"}, {"BlockType": "WORD", "Confidence": 95.02770233154297, "Text": "$ 2.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09466269612312317, "Height": 0.02341347746551037, "Left": 0.21568511426448822, "Top": 0.412321001291275}, "Polygon": [{"X": 0.21568551659584045, "Y": 0.412321001291275}, {"X": 0.3103478252887726, "Y": 0.4123225808143616}, {"X": 0.31034737825393677, "Y": 0.43573448061943054}, {"X": 0.21568511426448822, "Y": 0.4357329308986664}]}, "Id": "d7c92d83-cb4a-43ea-96dd-b7c6586490c2"}, {"BlockType": "WORD", "Confidence": 92.53985595703125, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010660490952432156, "Height": 0.004895414225757122, "Left": 0.34020763635635376, "Top": 0.42685312032699585}, "Polygon": [{"X": 0.3402077257633209, "Y": 0.42685312032699585}, {"X": 0.3508681356906891, "Y": 0.4268532991409302}, {"X": 0.3508680462837219, "Y": 0.43174853920936584}, {"X": 0.34020763635635376, "Y": 0.4317483603954315}]}, "Id": "3cb06893-fc1f-479b-a08a-cf54370e2f16"}, {"BlockType": "WORD", "Confidence": 94.15019226074219, "Text": "$ 76.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09904225915670395, "Height": 0.024107329547405243, "Left": 0.3566935062408447, "Top": 0.41229671239852905}, "Polygon": [{"X": 0.35669395327568054, "Y": 0.41229671239852905}, {"X": 0.4557357728481293, "Y": 0.4122983515262604}, {"X": 0.45573529601097107, "Y": 0.4364040195941925}, {"X": 0.3566935062408447, "Y": 0.43640241026878357}]}, "Id": "c95c284b-abc8-411f-bbdc-16b5889e4494"}, {"BlockType": "WORD", "Confidence": 97.13169860839844, "Text": "Deductions -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.105254627764225, "Height": 0.012189981527626514, "Left": 0.5749068260192871, "Top": 0.4077560305595398}, "Polygon": [{"X": 0.5749070644378662, "Y": 0.4077560305595398}, {"X": 0.6801614761352539, "Y": 0.4077577590942383}, {"X": 0.68016117811203, "Y": 0.41994598507881165}, {"X": 0.5749068260192871, "Y": 0.41994425654411316}]}, "Id": "2a65cc71-b39f-4f29-b36a-1208dbf6bb54"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00947486236691475, "Height": 0.012610362842679024, "Left": 0.6903110146522522, "Top": 0.4080924987792969}, "Polygon": [{"X": 0.6903113126754761, "Y": 0.4080924987792969}, {"X": 0.6997858881950378, "Y": 0.4080926477909088}, {"X": 0.699785590171814, "Y": 0.42070284485816956}, {"X": 0.6903110146522522, "Y": 0.4207026958465576}]}, "Id": "335b067c-d6c8-46db-935b-edf3b6af7f33"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "RACKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06469547003507614, "Height": 0.011673357337713242, "Left": 0.11322901397943497, "Top": 0.44025877118110657}, "Polygon": [{"X": 0.11322920024394989, "Y": 0.44025877118110657}, {"X": 0.1779244840145111, "Y": 0.4402598440647125}, {"X": 0.1779242902994156, "Y": 0.4519321322441101}, {"X": 0.11322901397943497, "Y": 0.45193105936050415}]}, "Id": "6b74156a-846a-4c17-adbd-ea9afad40239"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "AMOUNT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08688537031412125, "Height": 0.011167263612151146, "Left": 0.22889640927314758, "Top": 0.4412931501865387}, "Polygon": [{"X": 0.2288966029882431, "Y": 0.4412931501865387}, {"X": 0.31578177213668823, "Y": 0.4412945508956909}, {"X": 0.3157815635204315, "Y": 0.4524604082107544}, {"X": 0.22889640927314758, "Y": 0.4524589776992798}]}, "Id": "1395ab12-edfe-4576-a1c5-9ecb330a198d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06591404974460602, "Height": 0.011532474309206009, "Left": 0.36435604095458984, "Top": 0.44172292947769165}, "Polygon": [{"X": 0.36435627937316895, "Y": 0.44172292947769165}, {"X": 0.43027010560035706, "Y": 0.4417240023612976}, {"X": 0.43026986718177795, "Y": 0.45325538516044617}, {"X": 0.36435604095458984, "Y": 0.4532543122768402}]}, "Id": "dd967a10-2095-457e-9c7c-2c7a5ec71173"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047779832035303116, "Height": 0.011783292517066002, "Left": 0.5780911445617676, "Top": 0.4598481357097626}, "Polygon": [{"X": 0.5780914425849915, "Y": 0.4598481357097626}, {"X": 0.6258710026741028, "Y": 0.45984891057014465}, {"X": 0.6258707642555237, "Y": 0.47163140773773193}, {"X": 0.5780911445617676, "Y": 0.47163063287734985}]}, "Id": "d18ee70d-f9f3-43d0-8cfe-313c29fc320a"}, {"BlockType": "WORD", "Confidence": 94.47843170166016, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010423669591546059, "Height": 0.008369053713977337, "Left": 0.6805227398872375, "Top": 0.46202975511550903}, "Polygon": [{"X": 0.6805229187011719, "Y": 0.46202975511550903}, {"X": 0.6909464001655579, "Y": 0.46202993392944336}, {"X": 0.6909462213516235, "Y": 0.47039881348609924}, {"X": 0.6805227398872375, "Y": 0.4703986346721649}]}, "Id": "c26294a5-d840-4dc8-8150-e5abe671f002"}, {"BlockType": "WORD", "Confidence": 88.3681411743164, "Text": "$ 76.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10767380893230438, "Height": 0.021547745913267136, "Left": 0.6950628757476807, "Top": 0.4513116478919983}, "Polygon": [{"X": 0.6950633525848389, "Y": 0.4513116478919983}, {"X": 0.8027366399765015, "Y": 0.45131340622901917}, {"X": 0.8027361631393433, "Y": 0.4728594124317169}, {"X": 0.6950628757476807, "Y": 0.47285765409469604}]}, "Id": "45b0acf3-0edc-4e19-866b-2da0115cc451"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "(<PERSON>ck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04627856984734535, "Height": 0.01383149717003107, "Left": 0.5782169103622437, "Top": 0.476834237575531}, "Polygon": [{"X": 0.5782172083854675, "Y": 0.476834237575531}, {"X": 0.6244954466819763, "Y": 0.4768349826335907}, {"X": 0.6244951486587524, "Y": 0.4906657338142395}, {"X": 0.5782169103622437, "Y": 0.4906650185585022}]}, "Id": "bc3ba64b-7c67-40d3-85de-3afdb03ea821"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "checks", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05356390401721001, "Height": 0.011893514543771744, "Left": 0.6300036311149597, "Top": 0.47719553112983704}, "Polygon": [{"X": 0.6300039291381836, "Y": 0.47719553112983704}, {"X": 0.6835675835609436, "Y": 0.4771963953971863}, {"X": 0.6835672855377197, "Y": 0.4890890419483185}, {"X": 0.6300036311149597, "Y": 0.48908817768096924}]}, "Id": "d49138e9-4133-4c58-9d86-9486b1a8e546"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "will", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0301912073045969, "Height": 0.0115840258076787, "Left": 0.68900066614151, "Top": 0.4774719178676605}, "Polygon": [{"X": 0.6890009045600891, "Y": 0.4774719178676605}, {"X": 0.71919184923172, "Y": 0.4774723947048187}, {"X": 0.7191916108131409, "Y": 0.48905593156814575}, {"X": 0.68900066614151, "Y": 0.48905545473098755}]}, "Id": "333c1450-c96a-40e5-a20b-0abf4f2e0106"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "now", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03370633348822594, "Height": 0.008383868262171745, "Left": 0.7248402237892151, "Top": 0.48070210218429565}, "Polygon": [{"X": 0.7248404026031494, "Y": 0.48070210218429565}, {"X": 0.7585465312004089, "Y": 0.48070263862609863}, {"X": 0.7585463523864746, "Y": 0.48908597230911255}, {"X": 0.7248402237892151, "Y": 0.48908543586730957}]}, "Id": "afb2a8ca-3643-4370-8fec-25fb50b52dee"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01910540461540222, "Height": 0.011481469497084618, "Left": 0.7637166976928711, "Top": 0.4774838387966156}, "Polygon": [{"X": 0.763716995716095, "Y": 0.4774838387966156}, {"X": 0.7828221321105957, "Y": 0.4774841368198395}, {"X": 0.7828218340873718, "Y": 0.48896530270576477}, {"X": 0.7637166976928711, "Y": 0.4889650046825409}]}, "Id": "1f6e4e24-4ef0-46ae-ba92-8dc2c1b45179"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "sent", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03198545426130295, "Height": 0.00970318354666233, "Left": 0.7875938415527344, "Top": 0.47969570755958557}, "Polygon": [{"X": 0.7875940799713135, "Y": 0.47969570755958557}, {"X": 0.8195793032646179, "Y": 0.47969624400138855}, {"X": 0.8195790648460388, "Y": 0.48939889669418335}, {"X": 0.7875938415527344, "Y": 0.48939839005470276}]}, "Id": "8a4fe8ea-3277-4e46-8e0e-dcf97c12614c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014915943145751953, "Height": 0.00948281865566969, "Left": 0.8247016072273254, "Top": 0.4799514412879944}, "Polygon": [{"X": 0.8247018456459045, "Y": 0.4799514412879944}, {"X": 0.8396175503730774, "Y": 0.4799516797065735}, {"X": 0.8396173119544983, "Y": 0.48943427205085754}, {"X": 0.8247016072273254, "Y": 0.48943403363227844}]}, "Id": "8d9ab00d-7652-4a39-8176-adc98e2fb7ab"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02467089705169201, "Height": 0.011496896855533123, "Left": 0.8447686433792114, "Top": 0.4779270589351654}, "Polygon": [{"X": 0.8447689414024353, "Y": 0.4779270589351654}, {"X": 0.8694395422935486, "Y": 0.47792744636535645}, {"X": 0.8694392442703247, "Y": 0.4894239604473114}, {"X": 0.8447686433792114, "Y": 0.48942357301712036}]}, "Id": "1700a1be-44e1-4b6f-bef3-736aab750597"}, {"BlockType": "WORD", "Confidence": 99.56233978271484, "Text": "broker.)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06317983567714691, "Height": 0.014271195977926254, "Left": 0.5883939862251282, "Top": 0.4937463700771332}, "Polygon": [{"X": 0.588394284248352, "Y": 0.4937463700771332}, {"X": 0.6515738368034363, "Y": 0.49374738335609436}, {"X": 0.6515734791755676, "Y": 0.5080175399780273}, {"X": 0.5883939862251282, "Y": 0.5080165266990662}]}, "Id": "dbaae6d0-5f44-41d7-9dcf-b439c9343fde"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028301062062382698, "Height": 0.01154742669314146, "Left": 0.578912079334259, "Top": 0.5288403034210205}, "Polygon": [{"X": 0.5789123773574829, "Y": 0.5288403034210205}, {"X": 0.6072131395339966, "Y": 0.5288407206535339}, {"X": 0.6072129011154175, "Y": 0.540387749671936}, {"X": 0.578912079334259, "Y": 0.5403872728347778}]}, "Id": "5937b13a-9cac-44d8-831d-b034d73167fd"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036011919379234314, "Height": 0.011823967099189758, "Left": 0.6124662160873413, "Top": 0.5291033983230591}, "Polygon": [{"X": 0.6124664545059204, "Y": 0.5291033983230591}, {"X": 0.6484781503677368, "Y": 0.5291039347648621}, {"X": 0.6484779119491577, "Y": 0.5409273505210876}, {"X": 0.6124662160873413, "Y": 0.5409267544746399}]}, "Id": "27aa7e6a-9de2-4158-8e62-37d30c136292"}, {"BlockType": "WORD", "Confidence": 95.03826141357422, "Text": "$ 3854.00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13739635050296783, "Height": 0.0308965016156435, "Left": 0.6541860103607178, "Top": 0.5124000310897827}, "Polygon": [{"X": 0.6541867256164551, "Y": 0.5124000310897827}, {"X": 0.7915823459625244, "Y": 0.5124021768569946}, {"X": 0.7915816307067871, "Y": 0.5432965159416199}, {"X": 0.6541860103607178, "Y": 0.543294370174408}]}, "Id": "07da3d07-74ee-43ff-9c9f-6c9c77b7b788"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Billable", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10359985381364822, "Height": 0.01900283433496952, "Left": 0.11305779963731766, "Top": 0.5637322068214417}, "Polygon": [{"X": 0.11305809766054153, "Y": 0.5637322068214417}, {"X": 0.21665765345096588, "Y": 0.5637338161468506}, {"X": 0.21665732562541962, "Y": 0.5827350616455078}, {"X": 0.11305779963731766, "Y": 0.5827334523200989}]}, "Id": "d36e809a-f44f-481e-9ea8-509eb0256e6b"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Shortages:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13705690205097198, "Height": 0.02321109175682068, "Left": 0.2265913039445877, "Top": 0.5642426609992981}, "Polygon": [{"X": 0.22659169137477875, "Y": 0.5642426609992981}, {"X": 0.3636482059955597, "Y": 0.5642447471618652}, {"X": 0.3636477589607239, "Y": 0.5874537229537964}, {"X": 0.2265913039445877, "Y": 0.5874516367912292}]}, "Id": "55803698-8d7f-461c-9de3-b15c290b808f"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "(equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061043452471494675, "Height": 0.01005107443779707, "Left": 0.11157942563295364, "Top": 0.5891367197036743}, "Polygon": [{"X": 0.11157958954572678, "Y": 0.5891367197036743}, {"X": 0.17262287437915802, "Y": 0.589137613773346}, {"X": 0.1726227104663849, "Y": 0.5991877913475037}, {"X": 0.11157942563295364, "Y": 0.5991868376731873}]}, "Id": "6141deea-6d71-4314-b6b2-736399abf17d"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "missing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041543908417224884, "Height": 0.010033677332103252, "Left": 0.1757388710975647, "Top": 0.5894198417663574}, "Polygon": [{"X": 0.17573903501033783, "Y": 0.5894198417663574}, {"X": 0.21728277206420898, "Y": 0.58942049741745}, {"X": 0.21728260815143585, "Y": 0.5994535088539124}, {"X": 0.1757388710975647, "Y": 0.5994529128074646}]}, "Id": "60f72ee6-9b42-4acd-b8eb-3f0622a1b4a0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023842886090278625, "Height": 0.008355330675840378, "Left": 0.22032853960990906, "Top": 0.5890501737594604}, "Polygon": [{"X": 0.220328688621521, "Y": 0.5890501737594604}, {"X": 0.24417142570018768, "Y": 0.5890505313873291}, {"X": 0.24417129158973694, "Y": 0.5974054932594299}, {"X": 0.22032853960990906, "Y": 0.5974051356315613}]}, "Id": "441dbcbf-fd26-44be-baeb-fc02db175c56"}, {"BlockType": "WORD", "Confidence": 99.39472961425781, "Text": "insufficient", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06067577004432678, "Height": 0.008383647538721561, "Left": 0.24727027118206024, "Top": 0.58913254737854}, "Polygon": [{"X": 0.24727042019367218, "Y": 0.58913254737854}, {"X": 0.3079460561275482, "Y": 0.5891335010528564}, {"X": 0.3079459071159363, "Y": 0.5975161790847778}, {"X": 0.24727027118206024, "Y": 0.5975152850151062}]}, "Id": "4abc8248-1354-4e27-a4e6-da336b3f1578"}, {"BlockType": "WORD", "Confidence": 69.12089538574219, "Text": "documentation)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08395300805568695, "Height": 0.00893077626824379, "Left": 0.31085070967674255, "Top": 0.5895124673843384}, "Polygon": [{"X": 0.3108508884906769, "Y": 0.5895124673843384}, {"X": 0.3948037326335907, "Y": 0.5895137786865234}, {"X": 0.39480355381965637, "Y": 0.5984432697296143}, {"X": 0.31085070967674255, "Y": 0.5984419584274292}]}, "Id": "74860097-1ac6-4795-a32f-92ff48680aa6"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Racks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05259017273783684, "Height": 0.01186605729162693, "Left": 0.1125577911734581, "Top": 0.6129736304283142}, "Polygon": [{"X": 0.11255797743797302, "Y": 0.6129736304283142}, {"X": 0.16514796018600464, "Y": 0.6129744052886963}, {"X": 0.16514776647090912, "Y": 0.624839723110199}, {"X": 0.1125577911734581, "Y": 0.6248388886451721}]}, "Id": "6351e087-53be-47c9-8fbb-42b4e0ed9b0a"}, {"BlockType": "WORD", "Confidence": 93.86320495605469, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006043412256985903, "Height": 0.014050362631678581, "Left": 0.1713380217552185, "Top": 0.6129820346832275}, "Polygon": [{"X": 0.1713382601737976, "Y": 0.6129820346832275}, {"X": 0.17738144099712372, "Y": 0.6129821538925171}, {"X": 0.17738120257854462, "Y": 0.6270323991775513}, {"X": 0.1713380217552185, "Y": 0.6270323395729065}]}, "Id": "abf8e47f-9df7-45fa-8764-326f315bebdb"}, {"BlockType": "WORD", "Confidence": 95.3858413696289, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006358584389090538, "Height": 0.013822013512253761, "Left": 0.19258125126361847, "Top": 0.6134427189826965}, "Polygon": [{"X": 0.19258147478103638, "Y": 0.6134427189826965}, {"X": 0.19893983006477356, "Y": 0.6134428381919861}, {"X": 0.19893960654735565, "Y": 0.6272647380828857}, {"X": 0.19258125126361847, "Y": 0.627264678478241}]}, "Id": "6c9cec6e-cd37-4500-a648-63059bcdaeb2"}, {"BlockType": "WORD", "Confidence": 90.41952514648438, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009396913461387157, "Height": 0.008421574719250202, "Left": 0.20446345210075378, "Top": 0.61635822057724}, "Polygon": [{"X": 0.20446358621120453, "Y": 0.61635822057724}, {"X": 0.21386036276817322, "Y": 0.6163583993911743}, {"X": 0.21386021375656128, "Y": 0.6247798204421997}, {"X": 0.20446345210075378, "Y": 0.6247797012329102}]}, "Id": "ff726279-acd0-474a-b9ba-b053f7d12128"}, {"BlockType": "WORD", "Confidence": 98.662109375, "Text": "$250.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06485007703304291, "Height": 0.012917011976242065, "Left": 0.21900081634521484, "Top": 0.61274254322052}, "Polygon": [{"X": 0.21900103986263275, "Y": 0.61274254322052}, {"X": 0.28385090827941895, "Y": 0.6127434968948364}, {"X": 0.28385066986083984, "Y": 0.6256595253944397}, {"X": 0.21900081634521484, "Y": 0.6256585717201233}]}, "Id": "38ff40f6-3d2f-4947-b947-f765e8555cec"}, {"BlockType": "WORD", "Confidence": 96.6709213256836, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010482853278517723, "Height": 0.004838877357542515, "Left": 0.2889236509799957, "Top": 0.6166980862617493}, "Polygon": [{"X": 0.2889237403869629, "Y": 0.6166980862617493}, {"X": 0.2994065284729004, "Y": 0.6166982650756836}, {"X": 0.2994064390659332, "Y": 0.6215369701385498}, {"X": 0.2889236509799957, "Y": 0.6215367913246155}]}, "Id": "212d8090-15d1-4ae1-a92c-c15155b34cea"}, {"BlockType": "WORD", "Confidence": 97.64269256591797, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00942673534154892, "Height": 0.01252716314047575, "Left": 0.3047795295715332, "Top": 0.6132623553276062}, "Polygon": [{"X": 0.3047797381877899, "Y": 0.6132623553276062}, {"X": 0.3142062723636627, "Y": 0.6132624745368958}, {"X": 0.3142060339450836, "Y": 0.6257895231246948}, {"X": 0.3047795295715332, "Y": 0.6257894039154053}]}, "Id": "c244ec46-c52f-4e92-8d2f-69d1ec00489e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04172708839178085, "Height": 0.011693873442709446, "Left": 0.5969895720481873, "Top": 0.614647626876831}, "Polygon": [{"X": 0.5969898700714111, "Y": 0.614647626876831}, {"X": 0.6387166976928711, "Y": 0.6146482825279236}, {"X": 0.6387163996696472, "Y": 0.6263415217399597}, {"X": 0.5969895720481873, "Y": 0.6263408660888672}]}, "Id": "9e0ce7e2-e249-42ae-a924-9291c5ae5dbc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Deductions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09054192155599594, "Height": 0.011945457197725773, "Left": 0.6448025703430176, "Top": 0.6147304773330688}, "Polygon": [{"X": 0.6448028683662415, "Y": 0.6147304773330688}, {"X": 0.7353444695472717, "Y": 0.6147318482398987}, {"X": 0.7353442311286926, "Y": 0.6266759037971497}, {"X": 0.6448025703430176, "Y": 0.6266745924949646}]}, "Id": "61f41e4c-1984-4c14-9c62-9f31701396e7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027677087113261223, "Height": 0.01140290405601263, "Left": 0.5990846753120422, "Top": 0.6325882077217102}, "Polygon": [{"X": 0.5990849137306213, "Y": 0.6325882077217102}, {"X": 0.6267617344856262, "Y": 0.6325886249542236}, {"X": 0.6267614960670471, "Y": 0.6439911127090454}, {"X": 0.5990846753120422, "Y": 0.643990695476532}]}, "Id": "f3e53045-b4b2-4f55-bd22-356ca3662cef"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Load:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044476743787527084, "Height": 0.011937065981328487, "Left": 0.6315288543701172, "Top": 0.6326166391372681}, "Polygon": [{"X": 0.6315290927886963, "Y": 0.6326166391372681}, {"X": 0.6760055422782898, "Y": 0.6326172947883606}, {"X": 0.6760053038597107, "Y": 0.6445537209510803}, {"X": 0.6315288543701172, "Y": 0.6445530652999878}]}, "Id": "b26b38e5-8b24-4191-b1f1-8417ddf27f9a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039341285824775696, "Height": 0.011910246685147285, "Left": 0.11301317811012268, "Top": 0.6478856801986694}, "Polygon": [{"X": 0.1130133643746376, "Y": 0.6478856801986694}, {"X": 0.15235446393489838, "Y": 0.6478862762451172}, {"X": 0.15235427021980286, "Y": 0.6597959399223328}, {"X": 0.11301317811012268, "Y": 0.659795343875885}]}, "Id": "0f494b0b-3a26-4656-813c-8274c710eab1"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "Locks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05278314650058746, "Height": 0.012303642928600311, "Left": 0.15828214585781097, "Top": 0.6478525400161743}, "Polygon": [{"X": 0.1582823395729065, "Y": 0.6478525400161743}, {"X": 0.21106529235839844, "Y": 0.6478533148765564}, {"X": 0.21106508374214172, "Y": 0.6601561903953552}, {"X": 0.15828214585781097, "Y": 0.6601554155349731}]}, "Id": "968d293c-998f-478e-87ae-328ceb6109b4"}, {"BlockType": "WORD", "Confidence": 93.1861801147461, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005562262609601021, "Height": 0.013963066972792149, "Left": 0.21727655827999115, "Top": 0.6483426094055176}, "Polygon": [{"X": 0.21727679669857025, "Y": 0.6483426094055176}, {"X": 0.22283883392810822, "Y": 0.6483427286148071}, {"X": 0.2228385955095291, "Y": 0.6623057126998901}, {"X": 0.21727655827999115, "Y": 0.6623055934906006}]}, "Id": "333286eb-b9ae-405d-9920-7d04cc0fe5ff"}, {"BlockType": "WORD", "Confidence": 95.39321899414062, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005145468283444643, "Height": 0.01342238299548626, "Left": 0.2389434278011322, "Top": 0.6489519476890564}, "Polygon": [{"X": 0.2389436662197113, "Y": 0.6489519476890564}, {"X": 0.24408890306949615, "Y": 0.6489520072937012}, {"X": 0.24408866465091705, "Y": 0.6623743176460266}, {"X": 0.2389434278011322, "Y": 0.6623742580413818}]}, "Id": "1a2c2326-e8b8-40f6-9886-b1ac53a7cec7"}, {"BlockType": "WORD", "Confidence": 88.5899658203125, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009109045378863811, "Height": 0.008489411324262619, "Left": 0.2502589821815491, "Top": 0.6517046689987183}, "Polygon": [{"X": 0.250259131193161, "Y": 0.6517046689987183}, {"X": 0.25936803221702576, "Y": 0.6517047882080078}, {"X": 0.2593678832054138, "Y": 0.6601940393447876}, {"X": 0.2502589821815491, "Y": 0.660193920135498}]}, "Id": "14bf7af6-82ad-465d-8e34-763f8f699cf0"}, {"BlockType": "WORD", "Confidence": 96.75701141357422, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054991550743579865, "Height": 0.012697192840278149, "Left": 0.2649538815021515, "Top": 0.6482356190681458}, "Polygon": [{"X": 0.2649540901184082, "Y": 0.6482356190681458}, {"X": 0.31994542479515076, "Y": 0.6482364535331726}, {"X": 0.31994521617889404, "Y": 0.6609328389167786}, {"X": 0.2649538815021515, "Y": 0.6609320044517517}]}, "Id": "b542a72e-7541-473e-b2ba-99c82d3ffe25"}, {"BlockType": "WORD", "Confidence": 97.19168853759766, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010689365677535534, "Height": 0.004620452877134085, "Left": 0.3246465027332306, "Top": 0.6522577404975891}, "Polygon": [{"X": 0.32464659214019775, "Y": 0.6522577404975891}, {"X": 0.3353358805179596, "Y": 0.6522579193115234}, {"X": 0.33533579111099243, "Y": 0.6568782329559326}, {"X": 0.3246465027332306, "Y": 0.6568780541419983}]}, "Id": "fc1cf4ce-6fac-4de8-b25a-f4d68360b4cb"}, {"BlockType": "WORD", "Confidence": 98.12320709228516, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00922439992427826, "Height": 0.01231613289564848, "Left": 0.3406377136707306, "Top": 0.6484963297843933}, "Polygon": [{"X": 0.3406379520893097, "Y": 0.6484963297843933}, {"X": 0.34986212849617004, "Y": 0.6484964489936829}, {"X": 0.34986189007759094, "Y": 0.6608124375343323}, {"X": 0.3406377136707306, "Y": 0.6608123183250427}]}, "Id": "d2659b54-ee7d-4735-92f2-34feffc4efab"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009334661066532135, "Height": 0.01311455387622118, "Left": 0.598357617855072, "Top": 0.6671093106269836}, "Polygon": [{"X": 0.5983579158782959, "Y": 0.6671093106269836}, {"X": 0.607692301273346, "Y": 0.6671094298362732}, {"X": 0.6076920032501221, "Y": 0.680223822593689}, {"X": 0.598357617855072, "Y": 0.6802237033843994}]}, "Id": "ccd80a6a-48df-4a65-a5d6-a819f3a4bea3"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04449126496911049, "Height": 0.012075070291757584, "Left": 0.11303284019231796, "Top": 0.6828960180282593}, "Polygon": [{"X": 0.11303302645683289, "Y": 0.6828960180282593}, {"X": 0.15752410888671875, "Y": 0.6828966736793518}, {"X": 0.15752391517162323, "Y": 0.6949710845947266}, {"X": 0.11303284019231796, "Y": 0.694970428943634}]}, "Id": "a4c03ac6-8ae0-40e2-9fcc-5fc87fc125a4"}, {"BlockType": "WORD", "Confidence": 99.16454315185547, "Text": "<PERSON>:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04022518917918205, "Height": 0.011940435506403446, "Left": 0.1619383692741394, "Top": 0.6832626461982727}, "Polygon": [{"X": 0.16193856298923492, "Y": 0.6832626461982727}, {"X": 0.20216356217861176, "Y": 0.6832632422447205}, {"X": 0.20216336846351624, "Y": 0.6952030658721924}, {"X": 0.1619383692741394, "Y": 0.6952025294303894}]}, "Id": "5efc41f8-283a-4c3c-a95c-9af2f1c1dd0e"}, {"BlockType": "WORD", "Confidence": 81.13966369628906, "Text": "( )", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027367355301976204, "Height": 0.01416676677763462, "Left": 0.20837931334972382, "Top": 0.683368444442749}, "Polygon": [{"X": 0.20837955176830292, "Y": 0.683368444442749}, {"X": 0.23574666678905487, "Y": 0.6833688020706177}, {"X": 0.23574642837047577, "Y": 0.6975352168083191}, {"X": 0.20837931334972382, "Y": 0.6975347995758057}]}, "Id": "d9e6d6f7-2e5d-4476-8a06-6bce13c0eb88"}, {"BlockType": "WORD", "Confidence": 93.47417449951172, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008409181609749794, "Height": 0.008375664241611958, "Left": 0.24171757698059082, "Top": 0.6871090531349182}, "Polygon": [{"X": 0.24171772599220276, "Y": 0.6871090531349182}, {"X": 0.25012677907943726, "Y": 0.6871091723442078}, {"X": 0.2501266300678253, "Y": 0.6954846978187561}, {"X": 0.24171757698059082, "Y": 0.6954845786094666}]}, "Id": "0d93b033-6e7c-4dd0-95d9-8e0743765143"}, {"BlockType": "WORD", "Confidence": 99.267578125, "Text": "$500.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0642840787768364, "Height": 0.012623707763850689, "Left": 0.2565581202507019, "Top": 0.6835521459579468}, "Polygon": [{"X": 0.2565583288669586, "Y": 0.6835521459579468}, {"X": 0.3208422064781189, "Y": 0.6835530400276184}, {"X": 0.3208419680595398, "Y": 0.6961758136749268}, {"X": 0.2565581202507019, "Y": 0.6961749196052551}]}, "Id": "f686a88d-887a-43ae-bf1e-8dbc0a1d927e"}, {"BlockType": "WORD", "Confidence": 97.10618591308594, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010384739376604557, "Height": 0.004902643617242575, "Left": 0.32593783736228943, "Top": 0.6873841285705566}, "Polygon": [{"X": 0.3259379267692566, "Y": 0.6873841285705566}, {"X": 0.3363225758075714, "Y": 0.687384307384491}, {"X": 0.33632248640060425, "Y": 0.6922867894172668}, {"X": 0.32593783736228943, "Y": 0.6922866106033325}]}, "Id": "8d81deba-30b1-4307-8dd2-e7010deed7de"}, {"BlockType": "WORD", "Confidence": 98.3555908203125, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008791224099695683, "Height": 0.012004685588181019, "Left": 0.3419446349143982, "Top": 0.6839820146560669}, "Polygon": [{"X": 0.3419448733329773, "Y": 0.6839820146560669}, {"X": 0.3507358729839325, "Y": 0.6839821338653564}, {"X": 0.3507356345653534, "Y": 0.6959866881370544}, {"X": 0.3419446349143982, "Y": 0.6959865689277649}]}, "Id": "0e6d24bf-6afd-4220-aa44-8680f76dc83a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03910048305988312, "Height": 0.011792158707976341, "Left": 0.11297643184661865, "Top": 0.7173449993133545}, "Polygon": [{"X": 0.11297661811113358, "Y": 0.7173449993133545}, {"X": 0.15207691490650177, "Y": 0.7173455357551575}, {"X": 0.15207672119140625, "Y": 0.7291371822357178}, {"X": 0.11297643184661865, "Y": 0.72913658618927}]}, "Id": "421b03ef-4509-4fd1-a552-4dc3601cb595"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Straps:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054399095475673676, "Height": 0.014453202486038208, "Left": 0.15779224038124084, "Top": 0.7178453207015991}, "Polygon": [{"X": 0.15779247879981995, "Y": 0.7178453207015991}, {"X": 0.21219132840633392, "Y": 0.7178460955619812}, {"X": 0.21219108998775482, "Y": 0.7322985529899597}, {"X": 0.15779224038124084, "Y": 0.7322977781295776}]}, "Id": "e4bee59c-7b13-4816-9f04-3c4bc9228276"}, {"BlockType": "WORD", "Confidence": 95.66426086425781, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005661884788423777, "Height": 0.013633540831506252, "Left": 0.21844077110290527, "Top": 0.7184888124465942}, "Polygon": [{"X": 0.21844100952148438, "Y": 0.7184888124465942}, {"X": 0.2241026610136032, "Y": 0.718488872051239}, {"X": 0.2241024225950241, "Y": 0.7321223616600037}, {"X": 0.21844077110290527, "Y": 0.7321223020553589}]}, "Id": "85279db9-6de8-4e81-b7d5-dffe11bc777d"}, {"BlockType": "WORD", "Confidence": 92.70966339111328, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0046907952055335045, "Height": 0.012717517092823982, "Left": 0.24072834849357605, "Top": 0.7188562750816345}, "Polygon": [{"X": 0.24072855710983276, "Y": 0.7188562750816345}, {"X": 0.24541914463043213, "Y": 0.7188563346862793}, {"X": 0.24541892111301422, "Y": 0.7315738201141357}, {"X": 0.24072834849357605, "Y": 0.731573760509491}]}, "Id": "b5a6feac-2645-4a4a-a349-06eb6bcd3011"}, {"BlockType": "WORD", "Confidence": 90.41155242919922, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009888174943625927, "Height": 0.008589942008256912, "Left": 0.25075021386146545, "Top": 0.7209699749946594}, "Polygon": [{"X": 0.250750333070755, "Y": 0.7209699749946594}, {"X": 0.26063838601112366, "Y": 0.720970094203949}, {"X": 0.2606382369995117, "Y": 0.7295598983764648}, {"X": 0.25075021386146545, "Y": 0.7295597791671753}]}, "Id": "69ba994e-546a-4327-9595-ef12c93efe34"}, {"BlockType": "WORD", "Confidence": 96.35503387451172, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0543489009141922, "Height": 0.012356111779808998, "Left": 0.2662380635738373, "Top": 0.7175987958908081}, "Polygon": [{"X": 0.266238272190094, "Y": 0.7175987958908081}, {"X": 0.3205869495868683, "Y": 0.7175995707511902}, {"X": 0.3205867409706116, "Y": 0.7299548983573914}, {"X": 0.2662380635738373, "Y": 0.729954183101654}]}, "Id": "543fc47a-894d-471b-971d-9ac3d3cdb3fa"}, {"BlockType": "WORD", "Confidence": 96.7490463256836, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010925846174359322, "Height": 0.004703600890934467, "Left": 0.32567450404167175, "Top": 0.7218404412269592}, "Polygon": [{"X": 0.3256745934486389, "Y": 0.7218404412269592}, {"X": 0.3366003632545471, "Y": 0.7218406200408936}, {"X": 0.33660027384757996, "Y": 0.7265440821647644}, {"X": 0.32567450404167175, "Y": 0.7265439033508301}]}, "Id": "f95793ca-8801-460e-b5ea-dd39d669f73d"}, {"BlockType": "WORD", "Confidence": 98.55309295654297, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009500005282461643, "Height": 0.012313627637922764, "Left": 0.3416149914264679, "Top": 0.717765212059021}, "Polygon": [{"X": 0.341615229845047, "Y": 0.717765212059021}, {"X": 0.35111498832702637, "Y": 0.7177653312683105}, {"X": 0.35111477971076965, "Y": 0.7300788760185242}, {"X": 0.3416149914264679, "Y": 0.7300786972045898}]}, "Id": "98e9b7fb-f66b-4ce1-a4be-7352d317ce17"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Paperwork", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08504622429609299, "Height": 0.014969896525144577, "Left": 0.11243946850299835, "Top": 0.7521683573722839}, "Polygon": [{"X": 0.11243970692157745, "Y": 0.7521683573722839}, {"X": 0.19748568534851074, "Y": 0.7521695494651794}, {"X": 0.19748544692993164, "Y": 0.7671382427215576}, {"X": 0.11243946850299835, "Y": 0.7671370506286621}]}, "Id": "cc494057-95c3-4c4a-8651-7012602c8b63"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014554047957062721, "Height": 0.01138938870280981, "Left": 0.20301192998886108, "Top": 0.752566397190094}, "Polygon": [{"X": 0.2030121237039566, "Y": 0.752566397190094}, {"X": 0.21756598353385925, "Y": 0.7525665760040283}, {"X": 0.21756578981876373, "Y": 0.7639557719230652}, {"X": 0.20301192998886108, "Y": 0.7639555931091309}]}, "Id": "fdb8ebdf-ee33-47fa-b711-aa40cadf6dc1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "other", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04180818423628807, "Height": 0.011910613626241684, "Left": 0.22312361001968384, "Top": 0.7525661587715149}, "Polygon": [{"X": 0.22312380373477936, "Y": 0.7525661587715149}, {"X": 0.2649317979812622, "Y": 0.7525667548179626}, {"X": 0.2649315893650055, "Y": 0.7644767761230469}, {"X": 0.22312361001968384, "Y": 0.7644762396812439}]}, "Id": "6d658272-47d8-4f37-af0c-06d72c4ae05f"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Fines", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04254956915974617, "Height": 0.011971024796366692, "Left": 0.2693674862384796, "Top": 0.7526269555091858}, "Polygon": [{"X": 0.26936769485473633, "Y": 0.7526269555091858}, {"X": 0.3119170367717743, "Y": 0.7526275515556335}, {"X": 0.3119168281555176, "Y": 0.76459801197052}, {"X": 0.2693674862384796, "Y": 0.7645974159240723}]}, "Id": "3575ef6b-4119-4f6f-9e83-957f8825df3d"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009422391653060913, "Height": 0.012623455375432968, "Left": 0.3171099126338959, "Top": 0.7526003122329712}, "Polygon": [{"X": 0.3171101212501526, "Y": 0.7526003122329712}, {"X": 0.3265323042869568, "Y": 0.7526004314422607}, {"X": 0.3265320658683777, "Y": 0.7652237415313721}, {"X": 0.3171099126338959, "Y": 0.7652236223220825}]}, "Id": "4a579981-4583-463a-835c-d7f6027bf8c1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "(see", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03190310299396515, "Height": 0.013658314011991024, "Left": 0.112040214240551, "Top": 0.7703337073326111}, "Polygon": [{"X": 0.1120404303073883, "Y": 0.7703337073326111}, {"X": 0.14394330978393555, "Y": 0.7703341841697693}, {"X": 0.14394310116767883, "Y": 0.7839920520782471}, {"X": 0.112040214240551, "Y": 0.7839915752410889}]}, "Id": "f4639c08-1c11-496b-b4c6-9a95a520b790"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "attached", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06550821661949158, "Height": 0.012227124534547329, "Left": 0.14878001809120178, "Top": 0.7696290016174316}, "Polygon": [{"X": 0.1487802118062973, "Y": 0.7696290016174316}, {"X": 0.21428823471069336, "Y": 0.7696298956871033}, {"X": 0.21428802609443665, "Y": 0.781856119632721}, {"X": 0.14878001809120178, "Y": 0.7818552255630493}]}, "Id": "fffc2345-28ea-4b41-a5f8-2711e2db99ec"}, {"BlockType": "WORD", "Confidence": 98.20631408691406, "Text": "documentation)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12482749670743942, "Height": 0.014544562436640263, "Left": 0.2195606529712677, "Top": 0.7701399922370911}, "Polygon": [{"X": 0.219560906291008, "Y": 0.7701399922370911}, {"X": 0.3443881571292877, "Y": 0.7701417207717896}, {"X": 0.34438788890838623, "Y": 0.7846845388412476}, {"X": 0.2195606529712677, "Y": 0.7846828103065491}]}, "Id": "45b58d23-dc2b-4ded-9e08-43ee081fc7c8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Manager", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07048454135656357, "Height": 0.014754382893443108, "Left": 0.5434603691101074, "Top": 0.7714486718177795}, "Polygon": [{"X": 0.5434606671333313, "Y": 0.7714486718177795}, {"X": 0.613944947719574, "Y": 0.7714496850967407}, {"X": 0.6139445900917053, "Y": 0.7862030863761902}, {"X": 0.5434603691101074, "Y": 0.7862021327018738}]}, "Id": "674e0b93-2c3b-427e-b46b-b2c442b21853"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Approval:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07916168123483658, "Height": 0.014601444825530052, "Left": 0.6183347702026367, "Top": 0.7721040844917297}, "Polygon": [{"X": 0.6183351278305054, "Y": 0.7721040844917297}, {"X": 0.6974964737892151, "Y": 0.7721051573753357}, {"X": 0.6974961161613464, "Y": 0.7867055535316467}, {"X": 0.6183347702026367, "Y": 0.786704421043396}]}, "Id": "286f44ac-6c1d-40a5-911e-cdcd218f09d8"}, {"BlockType": "WORD", "Confidence": 77.2520751953125, "Text": "my", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0913793221116066, "Height": 0.0678924173116684, "Left": 0.7808471322059631, "Top": 0.7411839365959167}, "Polygon": [{"X": 0.7808487415313721, "Y": 0.7411839365959167}, {"X": 0.8722264766693115, "Y": 0.741185188293457}, {"X": 0.872224748134613, "Y": 0.8090763092041016}, {"X": 0.7808471322059631, "Y": 0.809075117111206}]}, "Id": "ca53f360-3e31-46c2-99dd-20f0508dcb1c"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Notes:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05062611773610115, "Height": 0.01179698295891285, "Left": 0.1118214800953865, "Top": 0.8050987124443054}, "Polygon": [{"X": 0.11182166635990143, "Y": 0.8050987124443054}, {"X": 0.16244760155677795, "Y": 0.8050994277000427}, {"X": 0.16244740784168243, "Y": 0.8168957233428955}, {"X": 0.1118214800953865, "Y": 0.8168950080871582}]}, "Id": "6854a02f-3096-4765-912d-cc55aaa5c634"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0286316629499197, "Height": 0.011854135431349277, "Left": 0.11139941215515137, "Top": 0.8565709590911865}, "Polygon": [{"X": 0.11139959841966629, "Y": 0.8565709590911865}, {"X": 0.14003106951713562, "Y": 0.8565713167190552}, {"X": 0.1400308907032013, "Y": 0.8684250712394714}, {"X": 0.11139941215515137, "Y": 0.8684247136116028}]}, "Id": "ab515ac2-7be9-4cfe-9518-6cd295de925a"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "fine", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02575264871120453, "Height": 0.00978795625269413, "Left": 0.14406681060791016, "Top": 0.8560076355934143}, "Polygon": [{"X": 0.1440669745206833, "Y": 0.8560076355934143}, {"X": 0.16981945931911469, "Y": 0.856007993221283}, {"X": 0.16981931030750275, "Y": 0.8657956123352051}, {"X": 0.14406681060791016, "Y": 0.8657952547073364}]}, "Id": "dfe2d583-62f6-47bc-a593-18bd175a18e3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "not", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02144373580813408, "Height": 0.007876353338360786, "Left": 0.17372062802314758, "Top": 0.858581006526947}, "Polygon": [{"X": 0.17372074723243713, "Y": 0.858581006526947}, {"X": 0.19516435265541077, "Y": 0.8585813045501709}, {"X": 0.19516423344612122, "Y": 0.8664573431015015}, {"X": 0.17372062802314758, "Y": 0.8664571046829224}]}, "Id": "6e0d9a2c-f5be-4d04-be4f-3b275004fc06"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "appealed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05893377587199211, "Height": 0.012434960342943668, "Left": 0.19908124208450317, "Top": 0.856492817401886}, "Polygon": [{"X": 0.1990814507007599, "Y": 0.856492817401886}, {"X": 0.2580150067806244, "Y": 0.8564935922622681}, {"X": 0.2580147981643677, "Y": 0.8689277768135071}, {"X": 0.19908124208450317, "Y": 0.868927001953125}]}, "Id": "c2cc48e9-31e9-4a8a-a940-c121bcb6993c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "within", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04209720343351364, "Height": 0.009921425022184849, "Left": 0.26245537400245667, "Top": 0.8565422296524048}, "Polygon": [{"X": 0.262455552816391, "Y": 0.8565422296524048}, {"X": 0.3045525848865509, "Y": 0.8565427660942078}, {"X": 0.3045524060726166, "Y": 0.8664636611938477}, {"X": 0.26245537400245667, "Y": 0.8664631247520447}]}, "Id": "48e51092-0004-4c7f-8deb-9cf5f56346d0"}, {"BlockType": "WORD", "Confidence": 99.81285858154297, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00717550003901124, "Height": 0.009535417892038822, "Left": 0.3089812994003296, "Top": 0.8568530082702637}, "Polygon": [{"X": 0.3089814782142639, "Y": 0.8568530082702637}, {"X": 0.316156804561615, "Y": 0.8568531274795532}, {"X": 0.31615662574768066, "Y": 0.8663884401321411}, {"X": 0.3089812994003296, "Y": 0.8663883209228516}]}, "Id": "eaaa0e8e-110b-419c-8e1f-bcebfc7f083a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "business", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05626513063907623, "Height": 0.009973298758268356, "Left": 0.32110944390296936, "Top": 0.8566691279411316}, "Polygon": [{"X": 0.3211096227169037, "Y": 0.8566691279411316}, {"X": 0.3773745596408844, "Y": 0.8566698431968689}, {"X": 0.3773743808269501, "Y": 0.866642415523529}, {"X": 0.32110944390296936, "Y": 0.8666417002677917}]}, "Id": "02ce8791-a72f-45f1-b99e-a3ed935b89bd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "days", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030406078323721886, "Height": 0.012324589304625988, "Left": 0.38138797879219055, "Top": 0.8570166826248169}, "Polygon": [{"X": 0.38138821721076965, "Y": 0.8570166826248169}, {"X": 0.4117940664291382, "Y": 0.8570170998573303}, {"X": 0.4117938280105591, "Y": 0.8693413138389587}, {"X": 0.38138797879219055, "Y": 0.8693408966064453}]}, "Id": "ffb4c098-6ef6-4748-82d2-33ad3ee8ac02"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01075731124728918, "Height": 0.010278955101966858, "Left": 0.416236937046051, "Top": 0.8568742871284485}, "Polygon": [{"X": 0.41623714566230774, "Y": 0.8568742871284485}, {"X": 0.426994264125824, "Y": 0.856874406337738}, {"X": 0.42699405550956726, "Y": 0.8671532273292542}, {"X": 0.416236937046051, "Y": 0.8671531081199646}]}, "Id": "f59c603b-86ef-4867-9ac5-55d2883c74ad"}, {"BlockType": "WORD", "Confidence": 88.87914276123047, "Text": "final.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033741485327482224, "Height": 0.010366929695010185, "Left": 0.4313061535358429, "Top": 0.8570934534072876}, "Polygon": [{"X": 0.4313063621520996, "Y": 0.8570934534072876}, {"X": 0.4650476574897766, "Y": 0.8570939302444458}, {"X": 0.4650474488735199, "Y": 0.8674604296684265}, {"X": 0.4313061535358429, "Y": 0.8674599528312683}]}, "Id": "07083b70-3311-4c0d-83d9-d840927b12c1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Appeals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05392080545425415, "Height": 0.012477932497859001, "Left": 0.4697865843772888, "Top": 0.8577681183815002}, "Polygon": [{"X": 0.4697868525981903, "Y": 0.8577681183815002}, {"X": 0.523707389831543, "Y": 0.8577688336372375}, {"X": 0.5237071514129639, "Y": 0.8702460527420044}, {"X": 0.4697865843772888, "Y": 0.8702453374862671}]}, "Id": "c6bd1fd6-e707-4c6d-be23-6b0281a58e09"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02868308126926422, "Height": 0.009493510238826275, "Left": 0.5273997783660889, "Top": 0.8604795932769775}, "Polygon": [{"X": 0.5273999571800232, "Y": 0.8604795932769775}, {"X": 0.5560828447341919, "Y": 0.8604799509048462}, {"X": 0.5560826659202576, "Y": 0.8699731230735779}, {"X": 0.5273997783660889, "Y": 0.8699727058410645}]}, "Id": "7fa23193-9985-4cb0-b1e8-2acb42161d9d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01582442969083786, "Height": 0.00984860584139824, "Left": 0.5604082942008972, "Top": 0.8578817248344421}, "Polygon": [{"X": 0.5604085326194763, "Y": 0.8578817248344421}, {"X": 0.5762327313423157, "Y": 0.8578819632530212}, {"X": 0.5762325525283813, "Y": 0.8677303194999695}, {"X": 0.5604082942008972, "Y": 0.8677301406860352}]}, "Id": "9b83f0c9-3a41-45ff-af5b-804f56dc9b44"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "made", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03545545041561127, "Height": 0.009668052196502686, "Left": 0.5805174112319946, "Top": 0.8583316206932068}, "Polygon": [{"X": 0.5805176496505737, "Y": 0.8583316206932068}, {"X": 0.6159728765487671, "Y": 0.858332097530365}, {"X": 0.6159726977348328, "Y": 0.8679996728897095}, {"X": 0.5805174112319946, "Y": 0.8679991960525513}]}, "Id": "25bf2115-a7ef-41d2-8dcc-b2452fd5fe08"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01208861730992794, "Height": 0.009668396785855293, "Left": 0.620679497718811, "Top": 0.8583553433418274}, "Polygon": [{"X": 0.6206797361373901, "Y": 0.8583553433418274}, {"X": 0.6327681541442871, "Y": 0.8583554625511169}, {"X": 0.632767915725708, "Y": 0.8680237531661987}, {"X": 0.620679497718811, "Y": 0.8680235743522644}]}, "Id": "c4461a95-771b-4770-8c58-9f5ff22c9209"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "writing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04751775413751602, "Height": 0.012706995010375977, "Left": 0.6371803879737854, "Top": 0.8582849502563477}, "Polygon": [{"X": 0.6371806263923645, "Y": 0.8582849502563477}, {"X": 0.6846981048583984, "Y": 0.8582856059074402}, {"X": 0.6846978664398193, "Y": 0.8709919452667236}, {"X": 0.6371803879737854, "Y": 0.8709913492202759}]}, "Id": "40c0af22-6e0c-4a2d-a5b5-bc4671e400b2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023504555225372314, "Height": 0.009705091826617718, "Left": 0.6888521313667297, "Top": 0.8586647510528564}, "Polygon": [{"X": 0.6888523697853088, "Y": 0.8586647510528564}, {"X": 0.712356686592102, "Y": 0.8586650490760803}, {"X": 0.712356448173523, "Y": 0.8683698773384094}, {"X": 0.6888521313667297, "Y": 0.8683695197105408}]}, "Id": "87f0ba7b-4c69-43f8-90b6-ecbd00869935"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "faxed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03634870797395706, "Height": 0.010283333249390125, "Left": 0.7171568274497986, "Top": 0.858481228351593}, "Polygon": [{"X": 0.7171570658683777, "Y": 0.858481228351593}, {"X": 0.753505527973175, "Y": 0.8584817051887512}, {"X": 0.753505289554596, "Y": 0.8687645792961121}, {"X": 0.7171568274497986, "Y": 0.8687641024589539}]}, "Id": "a66e82af-674d-44ac-ba68-b79edb6b5e37"}, {"BlockType": "WORD", "Confidence": 98.75538635253906, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013101203367114067, "Height": 0.00816251803189516, "Left": 0.7574577927589417, "Top": 0.8608255982398987}, "Polygon": [{"X": 0.757457971572876, "Y": 0.8608255982398987}, {"X": 0.7705589532852173, "Y": 0.8608257174491882}, {"X": 0.770558774471283, "Y": 0.8689880967140198}, {"X": 0.7574577927589417, "Y": 0.8689879179000854}]}, "Id": "438f569f-1a59-4507-928b-3def3913da29"}, {"BlockType": "WORD", "Confidence": 96.53240203857422, "Text": "(616)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03601207584142685, "Height": 0.011914408765733242, "Left": 0.7748721241950989, "Top": 0.8590317368507385}, "Polygon": [{"X": 0.7748724222183228, "Y": 0.8590317368507385}, {"X": 0.8108842372894287, "Y": 0.8590322136878967}, {"X": 0.8108839392662048, "Y": 0.8709461688995361}, {"X": 0.7748721241950989, "Y": 0.8709456920623779}]}, "Id": "0ae8bbd3-38bd-41e4-886d-9c21cd8bd88c"}, {"BlockType": "WORD", "Confidence": 98.642578125, "Text": "669-7468", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06304842978715897, "Height": 0.009987348690629005, "Left": 0.8146371245384216, "Top": 0.8590705990791321}, "Polygon": [{"X": 0.8146373629570007, "Y": 0.8590705990791321}, {"X": 0.877685546875, "Y": 0.8590714335441589}, {"X": 0.8776853084564209, "Y": 0.8690579533576965}, {"X": 0.8146371245384216, "Y": 0.8690571188926697}]}, "Id": "d0276b9a-7576-4035-9e1b-b7247f5cc49b"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "1cd18095-f374-4b25-8075-eaf787be26b6", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "1cd18095-f374-4b25-8075-eaf787be26b6", "content-type": "application/x-amz-json-1.1", "content-length": "85101", "date": "Thu, 25 Sep 2025 13:34:16 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "27056155-0571-4376-9c90-322177b7e427", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 25 Sep 2025 13:34:19 GMT", "content-type": "application/json", "content-length": "533", "connection": "keep-alive", "x-amzn-requestid": "27056155-0571-4376-9c90-322177b7e427"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. It's a \"Truckload Check-in Form\" and \"Clear-to-Pay Voucher\". It has \"Clear-to-Pay Voucher\" header. So doc type: clear_to_pay. There's no continuation. So output page 1 as clear_to_pay."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"clear_to_pay\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2787, "outputTokens": 75, "totalTokens": 2862}, "metrics": {"latencyMs": 517}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/560dbbe0_clear_to_pay.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence, follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n        STRICKLY USE classify_logistics_doc_type TOOL CALL TO GIVE STRUCTURED CLASSIFICATION DATA.\n\n    ## Model Instructions:\n        - PDF/image to text conversion is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination, Keywords indication and Key fields/structure are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice or Freight Bill\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header of pod.\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load confirmation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple types of documents on one page, Multiple headers with multiple keywords of different types of documents.\n        \n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation, Order Acknowledgement\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nLone Saw 8862654\nFMBVB\nSAWYER NURSERY.\nTruckload Check-in Form\nClear-to-Pay Voucher\nLoad # 20688 AC\nBroker PE\nDate Processed 5/6/24\nLoad Cost $3778.00\nRacks Returned 38\nSurcharge + $\n38 X $ 2.00 = $ 76.00\nDeductions - $\nRACKS AMOUNT TOTAL\nRacks\n+ $ 76.00\n(Rack checks will now be sent to the\nbroker.)\nNet Cost $ 3854.00\nBillable Shortages:\n(equipment missing with insufficient documentation)\nRacks: ( ) X $250.00 = $\nTotal Deductions\nFor Load:\nLoad Locks: ( ) X $25.00 = $\n$\nPallet Jack: ( ) X $500.00 = $\nLoad Straps: ( ) X $25.00 = $\nPaperwork & other Fines $\n(see attached documentation)\nManager Approval:\nmy\nNotes:\nAny fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616) 669-7468\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "comm_invoice", "lumper_receipt", "bol", "pod", "rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "customs_doc", "other", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}