{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.454081654548645, "Height": 0.7581125497817993, "Left": 0.25131797790527344, "Top": 0.13454341888427734}, "Polygon": [{"X": 0.25131797790527344, "Y": 0.15017805993556976}, {"X": 0.690207302570343, "Y": 0.13454341888427734}, {"X": 0.7053996324539185, "Y": 0.8770462870597839}, {"X": 0.2865610122680664, "Y": 0.8926559686660767}]}, "Id": "900983b2-1ad1-4c4f-8286-f705576cfa95", "Relationships": [{"Type": "CHILD", "Ids": ["f7b9e234-faca-4454-b3e1-54bcbca8812b", "50cf7978-917e-4dc1-a7e4-ced98a5e7722", "41868a7e-aa7b-4fad-84ab-d4d7d70168ac", "1007ebc6-5b84-45e3-8241-7830c14a62c4", "35eef686-a60a-439e-aed3-9a5a8261279b", "5573aeb2-c663-48e9-bb2b-761fb21d2fea", "846aa71f-eb0e-442e-996f-8d7a98cb2885", "89abceb2-7d23-4d4a-9368-b0cff96334e0", "c5ca3506-5a9d-42ff-b1fe-04c082f605a0", "dae4eeae-5fdb-416c-b377-3f84942491c9", "c6081e5f-5e78-4822-933e-016f98a54d5f", "f7dcd55a-2563-46f6-82e0-8131a2411c19", "577701ad-fe36-4944-959a-0f5e8fe0c420", "0c0385b5-52f2-43aa-94a4-575cb024b8bd", "d47718e6-9e2a-4ae6-80e5-b4cbeff3e003", "32909fa4-d87a-4e89-98af-f616bdfc3e27", "6abe1940-f916-463b-8863-6531f7e5f892", "2426013c-cb12-4c2b-8783-3db1f07bf114", "a890a906-c0d0-464b-a4f0-b7790765f7a3", "535c47ba-9116-4d86-87e7-77146b5e2e81", "91d8641a-a282-4093-91a4-02c3ed0963a2", "e8bcba22-a1d3-4bb4-8e9a-6a689baf71dc", "f0e0ebc2-3c6f-4bd2-a8b2-0f8c44f00be4", "861e1f60-446c-44b4-ae21-e550bac735c2", "5f2a38d5-0324-4a6a-8b57-2a61dadf8ece", "51eb437b-49a8-4dd9-add7-f63deee1b15a", "db5c798d-6613-4495-9d5f-3bfb8e823270", "9a0fec48-af92-45b5-901f-e008a2215db4", "8e037723-dd9c-44e7-ad91-7c5e4c62c91f"]}]}, {"BlockType": "LINE", "Confidence": 99.95063781738281, "Text": "KWIK TRIP #674", "Geometry": {"BoundingBox": {"Width": 0.19756744801998138, "Height": 0.023608893156051636, "Left": 0.36072900891304016, "Top": 0.36962443590164185}, "Polygon": [{"X": 0.36072900891304016, "Y": 0.37674590945243835}, {"X": 0.5578181147575378, "Y": 0.36962443590164185}, {"X": 0.5582964420318604, "Y": 0.3861120045185089}, {"X": 0.3614102005958557, "Y": 0.3932333290576935}]}, "Id": "f7b9e234-faca-4454-b3e1-54bcbca8812b", "Relationships": [{"Type": "CHILD", "Ids": ["cd4cb3bb-ae49-47bb-b2d6-979459ebad0d", "654bea73-97fd-42e8-a29c-8bf92f073afd", "3750ed45-68bd-4cc6-9e27-196ecb53f5ae"]}]}, {"BlockType": "LINE", "Confidence": 99.65913391113281, "Text": "6107 3M DR", "Geometry": {"BoundingBox": {"Width": 0.13964559137821198, "Height": 0.020413720980286598, "Left": 0.3908270299434662, "Top": 0.3920116722583771}, "Polygon": [{"X": 0.3908270299434662, "Y": 0.3970470130443573}, {"X": 0.5299991369247437, "Y": 0.3920116722583771}, {"X": 0.5304726362228394, "Y": 0.40739017724990845}, {"X": 0.39143431186676025, "Y": 0.4124253988265991}]}, "Id": "50cf7978-917e-4dc1-a7e4-ced98a5e7722", "Relationships": [{"Type": "CHILD", "Ids": ["f0efd540-ba35-4e23-9c27-80408d183346", "e9023b8f-53aa-498f-8261-44e16942e355", "6f5a11c4-8616-4e55-88a2-6ab5999ba004"]}]}, {"BlockType": "LINE", "Confidence": 98.72528839111328, "Text": "MENOMONIE, WI", "Geometry": {"BoundingBox": {"Width": 0.18348535895347595, "Height": 0.024856187403202057, "Left": 0.36184778809547424, "Top": 0.41202372312545776}, "Polygon": [{"X": 0.36184778809547424, "Y": 0.41865092515945435}, {"X": 0.5447880029678345, "Y": 0.41202372312545776}, {"X": 0.5453331470489502, "Y": 0.43025287985801697}, {"X": 0.3626016080379486, "Y": 0.4368799030780792}]}, "Id": "41868a7e-aa7b-4fad-84ab-d4d7d70168ac", "Relationships": [{"Type": "CHILD", "Ids": ["30775624-ee62-40f4-8135-a8794cdb6d89", "db17f85b-322c-4f8d-94ec-4298b411102b"]}]}, {"BlockType": "LINE", "Confidence": 98.86439514160156, "Text": "Invoice# 0091264", "Geometry": {"BoundingBox": {"Width": 0.23525457084178925, "Height": 0.024674849584698677, "Left": 0.3254137635231018, "Top": 0.45204710960388184}, "Polygon": [{"X": 0.3254137635231018, "Y": 0.46057361364364624}, {"X": 0.5601997971534729, "Y": 0.45204710960388184}, {"X": 0.5606683492660522, "Y": 0.46819573640823364}, {"X": 0.32612016797065735, "Y": 0.47672197222709656}]}, "Id": "1007ebc6-5b84-45e3-8241-7830c14a62c4", "Relationships": [{"Type": "CHILD", "Ids": ["5b8d5b9b-8fb7-4df7-9c8f-1974118c2558", "9171f4d1-06b1-41a6-9532-420c4800470f"]}]}, {"BlockType": "LINE", "Confidence": 98.19050598144531, "Text": "Trans # 0900-1264", "Geometry": {"BoundingBox": {"Width": 0.26495516300201416, "Height": 0.025396080687642097, "Left": 0.32348528504371643, "Top": 0.4718352258205414}, "Polygon": [{"X": 0.32348528504371643, "Y": 0.48145386576652527}, {"X": 0.5880096554756165, "Y": 0.4718352258205414}, {"X": 0.5884404182434082, "Y": 0.4876129627227783}, {"X": 0.3241782784461975, "Y": 0.49723130464553833}]}, "Id": "35eef686-a60a-439e-aed3-9a5a8261279b", "Relationships": [{"Type": "CHILD", "Ids": ["abe293b6-4fca-48be-91a2-1773300f730f", "d82453a5-ea48-4723-95ab-8875a0410061", "4090b750-5cf0-4200-8598-45c933a6bd8b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.05476602539420128, "Height": 0.015079988166689873, "Left": 0.326709121465683, "Top": 0.4999234676361084}, "Polygon": [{"X": 0.326709121465683, "Y": 0.5018981099128723}, {"X": 0.38094615936279297, "Y": 0.4999234676361084}, {"X": 0.38147515058517456, "Y": 0.5130288600921631}, {"X": 0.3272828459739685, "Y": 0.5150034427642822}]}, "Id": "5573aeb2-c663-48e9-bb2b-761fb21d2fea", "Relationships": [{"Type": "CHILD", "Ids": ["d5abf89b-11b9-47f9-8b54-4a454c02dfc1"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "10/14/2024", "Geometry": {"BoundingBox": {"Width": 0.13732492923736572, "Height": 0.020604457706212997, "Left": 0.4663439691066742, "Top": 0.49336540699005127}, "Polygon": [{"X": 0.4663439691066742, "Y": 0.4983505308628082}, {"X": 0.6032568216323853, "Y": 0.49336540699005127}, {"X": 0.6036689281463623, "Y": 0.5089849233627319}, {"X": 0.4668905735015869, "Y": 0.513969898223877}]}, "Id": "846aa71f-eb0e-442e-996f-8d7a98cb2885", "Relationships": [{"Type": "CHILD", "Ids": ["398e1316-e25b-46ed-b434-f6d0ff813191"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.05644448474049568, "Height": 0.01606922037899494, "Left": 0.32618141174316406, "Top": 0.5192623734474182}, "Polygon": [{"X": 0.32618141174316406, "Y": 0.5212991237640381}, {"X": 0.382059782743454, "Y": 0.5192623734474182}, {"X": 0.38262587785720825, "Y": 0.5332948565483093}, {"X": 0.3267969489097595, "Y": 0.5353315472602844}]}, "Id": "89abceb2-7d23-4d4a-9368-b0cff96334e0", "Relationships": [{"Type": "CHILD", "Ids": ["9d55f0f1-954a-41b1-9d42-b2d19c83a7ca"]}]}, {"BlockType": "LINE", "Confidence": 99.3092269897461, "Text": "04:31 PM", "Geometry": {"BoundingBox": {"Width": 0.10984169691801071, "Height": 0.01794710010290146, "Left": 0.46685606241226196, "Top": 0.5144302845001221}, "Polygon": [{"X": 0.46685606241226196, "Y": 0.5184202790260315}, {"X": 0.5763053894042969, "Y": 0.5144302845001221}, {"X": 0.5766977667808533, "Y": 0.5283874869346619}, {"X": 0.46734464168548584, "Y": 0.5323773622512817}]}, "Id": "c5ca3506-5a9d-42ff-b1fe-04c082f605a0", "Relationships": [{"Type": "CHILD", "Ids": ["25ccc348-01b1-41ff-a922-8ebb999c33c2", "f9a540bf-80ac-4b9e-a754-8996dc740866"]}]}, {"BlockType": "LINE", "Confidence": 99.9140396118164, "Text": "Pump Quantity Price", "Geometry": {"BoundingBox": {"Width": 0.2766816318035126, "Height": 0.028106873854994774, "Left": 0.32986927032470703, "Top": 0.5522388815879822}, "Polygon": [{"X": 0.32986927032470703, "Y": 0.5623319149017334}, {"X": 0.6060770750045776, "Y": 0.5522388815879822}, {"X": 0.606550931930542, "Y": 0.5702531933784485}, {"X": 0.33065730333328247, "Y": 0.5803457498550415}]}, "Id": "dae4eeae-5fdb-416c-b377-3f84942491c9", "Relationships": [{"Type": "CHILD", "Ids": ["fb55b050-8cea-43d5-a6be-7d02c636c852", "a5c63447-9ff2-485c-b043-244afbefc831", "9ba695ee-1fa3-4718-80fb-b900f34c38bb"]}]}, {"BlockType": "LINE", "Confidence": 99.52168273925781, "Text": "04", "Geometry": {"BoundingBox": {"Width": 0.02663435973227024, "Height": 0.014045603573322296, "Left": 0.3448355197906494, "Top": 0.5812066197395325}, "Polygon": [{"X": 0.3448355197906494, "Y": 0.5821613669395447}, {"X": 0.3709304630756378, "Y": 0.5812066197395325}, {"X": 0.3714698553085327, "Y": 0.5942975282669067}, {"X": 0.3453965187072754, "Y": 0.5952522158622742}]}, "Id": "c6081e5f-5e78-4822-933e-016f98a54d5f", "Relationships": [{"Type": "CHILD", "Ids": ["d0c531af-3cd9-447b-b5b6-af77a2cf964f"]}]}, {"BlockType": "LINE", "Confidence": 99.68679809570312, "Text": "171.800 3.589", "Geometry": {"BoundingBox": {"Width": 0.19322726130485535, "Height": 0.02314114198088646, "Left": 0.41411861777305603, "Top": 0.5732132792472839}, "Polygon": [{"X": 0.41411861777305603, "Y": 0.5802676677703857}, {"X": 0.6069230437278748, "Y": 0.5732132792472839}, {"X": 0.6073458790779114, "Y": 0.5893003940582275}, {"X": 0.4147375226020813, "Y": 0.5963544249534607}]}, "Id": "f7dcd55a-2563-46f6-82e0-8131a2411c19", "Relationships": [{"Type": "CHILD", "Ids": ["f14a1c97-4180-427c-a2a0-74c649d0340d", "684c953d-64d6-4477-9892-0060a680d1ca"]}]}, {"BlockType": "LINE", "Confidence": 99.392333984375, "Text": "04", "Geometry": {"BoundingBox": {"Width": 0.02648155763745308, "Height": 0.014114859513938427, "Left": 0.34591373801231384, "Top": 0.6015783548355103}, "Polygon": [{"X": 0.34591373801231384, "Y": 0.6025285720825195}, {"X": 0.37185290455818176, "Y": 0.6015783548355103}, {"X": 0.37239527702331543, "Y": 0.6147430539131165}, {"X": 0.3464777171611786, "Y": 0.615693211555481}]}, "Id": "577701ad-fe36-4944-959a-0f5e8fe0c420", "Relationships": [{"Type": "CHILD", "Ids": ["2c680210-4600-423d-b9e5-b5db2e719bb0"]}]}, {"BlockType": "LINE", "Confidence": 99.66358184814453, "Text": "20.692 4.189", "Geometry": {"BoundingBox": {"Width": 0.17895229160785675, "Height": 0.022649768739938736, "Left": 0.4293588399887085, "Top": 0.5935106873512268}, "Polygon": [{"X": 0.4293588399887085, "Y": 0.6000507473945618}, {"X": 0.6078881025314331, "Y": 0.5935106873512268}, {"X": 0.608311116695404, "Y": 0.6096206903457642}, {"X": 0.4299638867378235, "Y": 0.6161604523658752}]}, "Id": "0c0385b5-52f2-43aa-94a4-575cb024b8bd", "Relationships": [{"Type": "CHILD", "Ids": ["6870cb8c-3e6e-470a-b4ec-8c5213b60485", "2f350091-458c-4846-b688-138af2393c76"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.09675811976194382, "Height": 0.017069604247808456, "Left": 0.33332645893096924, "Top": 0.6404123306274414}, "Polygon": [{"X": 0.33332645893096924, "Y": 0.6439467668533325}, {"X": 0.429575115442276, "Y": 0.6404123306274414}, {"X": 0.43008458614349365, "Y": 0.6539475917816162}, {"X": 0.33391866087913513, "Y": 0.6574819087982178}]}, "Id": "d47718e6-9e2a-4ae6-80e5-b4cbeff3e003", "Relationships": [{"Type": "CHILD", "Ids": ["6a5392f6-e0cf-4327-8dd6-43f94081efa4"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.08349441736936569, "Height": 0.0188782699406147, "Left": 0.5274695754051208, "Top": 0.6347059011459351}, "Polygon": [{"X": 0.5274695754051208, "Y": 0.6377570033073425}, {"X": 0.6105499863624573, "Y": 0.6347059011459351}, {"X": 0.6109640002250671, "Y": 0.6505332589149475}, {"X": 0.5279670357704163, "Y": 0.6535841822624207}]}, "Id": "32909fa4-d87a-4e89-98af-f616bdfc3e27", "Relationships": [{"Type": "CHILD", "Ids": ["fb6992c0-afab-4f45-b742-53ed04a59a5d"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "DSL2", "Geometry": {"BoundingBox": {"Width": 0.05495951697230339, "Height": 0.01497382391244173, "Left": 0.33432072401046753, "Top": 0.6626691818237305}, "Polygon": [{"X": 0.33432072401046753, "Y": 0.6646707653999329}, {"X": 0.3887576460838318, "Y": 0.6626691818237305}, {"X": 0.3892802298069, "Y": 0.6756415367126465}, {"X": 0.334888219833374, "Y": 0.6776430010795593}]}, "Id": "6abe1940-f916-463b-8863-6531f7e5f892", "Relationships": [{"Type": "CHILD", "Ids": ["8f374f49-c1f2-48a1-b412-9bfd47c0cffb"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "616.59", "Geometry": {"BoundingBox": {"Width": 0.08259709924459457, "Height": 0.01827443577349186, "Left": 0.5286075472831726, "Top": 0.6554474830627441}, "Polygon": [{"X": 0.5286075472831726, "Y": 0.6584699153900146}, {"X": 0.6108053922653198, "Y": 0.6554474830627441}, {"X": 0.6112046241760254, "Y": 0.6706995964050293}, {"X": 0.5290864109992981, "Y": 0.6737219095230103}]}, "Id": "2426013c-cb12-4c2b-8783-3db1f07bf114", "Relationships": [{"Type": "CHILD", "Ids": ["b3e20d75-cc70-488d-ae3b-0bd72561b3bc"]}]}, {"BlockType": "LINE", "Confidence": 99.83238983154297, "Text": "DEFBULK", "Geometry": {"BoundingBox": {"Width": 0.09657197445631027, "Height": 0.016623321920633316, "Left": 0.33505934476852417, "Top": 0.6816321015357971}, "Polygon": [{"X": 0.33505934476852417, "Y": 0.6851691603660583}, {"X": 0.4311387538909912, "Y": 0.6816321015357971}, {"X": 0.43163132667541504, "Y": 0.6947185397148132}, {"X": 0.33563196659088135, "Y": 0.6982554793357849}]}, "Id": "a890a906-c0d0-464b-a4f0-b7790765f7a3", "Relationships": [{"Type": "CHILD", "Ids": ["a78b5ba1-846b-4081-9c5f-dea7fb7f6acf"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "86.68", "Geometry": {"BoundingBox": {"Width": 0.06907442212104797, "Height": 0.016308285295963287, "Left": 0.5434471964836121, "Top": 0.6766374111175537}, "Polygon": [{"X": 0.5434471964836121, "Y": 0.6791672706604004}, {"X": 0.6121616363525391, "Y": 0.6766374111175537}, {"X": 0.6125215888023376, "Y": 0.6904159188270569}, {"X": 0.5438673496246338, "Y": 0.692945659160614}]}, "Id": "535c47ba-9116-4d86-87e7-77146b5e2e81", "Relationships": [{"Type": "CHILD", "Ids": ["e8bb0228-35d6-4161-988a-fd894913541e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.06977012008428574, "Height": 0.015891438350081444, "Left": 0.33563369512557983, "Top": 0.7233551144599915}, "Polygon": [{"X": 0.33563369512557983, "Y": 0.725910484790802}, {"X": 0.404878169298172, "Y": 0.7233551144599915}, {"X": 0.4054037928581238, "Y": 0.7366912961006165}, {"X": 0.3362182676792145, "Y": 0.7392465472221375}]}, "Id": "91d8641a-a282-4093-91a4-02c3ed0963a2", "Relationships": [{"Type": "CHILD", "Ids": ["24235ba0-131d-4a5f-bc90-0d06839d865f"]}]}, {"BlockType": "LINE", "Confidence": 99.87065887451172, "Text": "703.27", "Geometry": {"BoundingBox": {"Width": 0.08198404312133789, "Height": 0.01752222515642643, "Left": 0.5179861187934875, "Top": 0.7172594666481018}, "Polygon": [{"X": 0.5179861187934875, "Y": 0.7202706933021545}, {"X": 0.5995784401893616, "Y": 0.7172594666481018}, {"X": 0.5999701619148254, "Y": 0.7317705750465393}, {"X": 0.5184533596038818, "Y": 0.7347816824913025}]}, "Id": "e8bcba22-a1d3-4bb4-8e9a-6a689baf71dc", "Relationships": [{"Type": "CHILD", "Ids": ["95c8ab09-7222-418a-99bc-e671152efe3a"]}]}, {"BlockType": "LINE", "Confidence": 99.8396987915039, "Text": "NBS/KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.26311570405960083, "Height": 0.025132104754447937, "Left": 0.3385973572731018, "Top": 0.7567457556724548}, "Polygon": [{"X": 0.3385973572731018, "Y": 0.7664640545845032}, {"X": 0.6012976169586182, "Y": 0.7567457556724548}, {"X": 0.6017130613327026, "Y": 0.7721601128578186}, {"X": 0.3392718434333801, "Y": 0.781877875328064}]}, "Id": "f0e0ebc2-3c6f-4bd2-a8b2-0f8c44f00be4", "Relationships": [{"Type": "CHILD", "Ids": ["a0f925bd-976f-46ef-a4fe-520f7d3f423e", "fbb982b6-3b8d-4249-95cd-3a1712a01c90", "a5d93a03-eef9-4079-a9d8-e34630743659"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.12422561645507812, "Height": 0.017659248784184456, "Left": 0.33996036648750305, "Top": 0.7825700044631958}, "Polygon": [{"X": 0.33996036648750305, "Y": 0.7871540188789368}, {"X": 0.*****************, "Y": 0.7825700044631958}, {"X": 0.****************, "Y": 0.7956455945968628}, {"X": 0.3405321538448334, "Y": 0.8002292513847351}]}, "Id": "861e1f60-446c-44b4-ae21-e550bac735c2", "Relationships": [{"Type": "CHILD", "Ids": ["60703bd1-4ffb-4d98-bc07-800b4694e8fc", "bbb6d481-4a19-44a9-8e54-f4bcf573182f"]}]}, {"BlockType": "LINE", "Confidence": 28.0056209564209, "Text": "XXXXXXXXXXXXXX1591", "Geometry": {"BoundingBox": {"Width": 0.24606989324092865, "Height": 0.021812358871102333, "Left": 0.3405051827430725, "Top": 0.7990379929542542}, "Polygon": [{"X": 0.3405051827430725, "Y": 0.8081507086753845}, {"X": 0.5862196087837219, "Y": 0.7990379929542542}, {"X": 0.5865750908851624, "Y": 0.8117382526397705}, {"X": 0.3410608470439911, "Y": 0.8208503723144531}]}, "Id": "5f2a38d5-0324-4a6a-8b57-2a61dadf8ece", "Relationships": [{"Type": "CHILD", "Ids": ["74993a48-2b44-4509-ad26-a5e01908ea2a"]}]}, {"BlockType": "LINE", "Confidence": 99.9365234375, "Text": "AUTH #", "Geometry": {"BoundingBox": {"Width": 0.08407134562730789, "Height": 0.016345659270882607, "Left": 0.34168991446495056, "Top": 0.8245274424552917}, "Polygon": [{"X": 0.34168991446495056, "Y": 0.8276301026344299}, {"X": 0.4252532124519348, "Y": 0.8245274424552917}, {"X": 0.42576128244400024, "Y": 0.8377705812454224}, {"X": 0.34226906299591064, "Y": 0.8408730626106262}]}, "Id": "51eb437b-49a8-4dd9-add7-f63deee1b15a", "Relationships": [{"Type": "CHILD", "Ids": ["31ae62b0-46b5-4346-8ed6-4b8a09cab829", "a9511491-d204-41d3-ac0a-8e2b4ded3bac"]}]}, {"BlockType": "LINE", "Confidence": 99.5517807006836, "Text": "P02385", "Geometry": {"BoundingBox": {"Width": 0.08157550543546677, "Height": 0.015940601006150246, "Left": 0.5366358160972595, "Top": 0.8179303407669067}, "Polygon": [{"X": 0.5366358160972595, "Y": 0.8209468126296997}, {"X": 0.617875337600708, "Y": 0.8179303407669067}, {"X": 0.6182113289833069, "Y": 0.830854594707489}, {"X": 0.5370392203330994, "Y": 0.8338708877563477}]}, "Id": "db5c798d-6613-4495-9d5f-3bfb8e823270", "Relationships": [{"Type": "CHILD", "Ids": ["c12e9136-a264-431b-96b6-6b2d13a14cf2"]}]}, {"BlockType": "LINE", "Confidence": 99.65631103515625, "Text": "RESPONSE: APPROVED", "Geometry": {"BoundingBox": {"Width": 0.24871289730072021, "Height": 0.022571705281734467, "Left": 0.34280648827552795, "Top": 0.8391445875167847}, "Polygon": [{"X": 0.34280648827552795, "Y": 0.8483769297599792}, {"X": 0.5911492705345154, "Y": 0.8391445875167847}, {"X": 0.5915194153785706, "Y": 0.8524845838546753}, {"X": 0.3433896601200104, "Y": 0.8617163300514221}]}, "Id": "9a0fec48-af92-45b5-901f-e008a2215db4", "Relationships": [{"Type": "CHILD", "Ids": ["1e3e79f0-8a94-4b0a-aec4-d366b14d1468", "888a7d1e-02d8-4995-84ee-830784704da9"]}]}, {"BlockType": "LINE", "Confidence": 99.56207275390625, "Text": "AUTH REF#: P02385", "Geometry": {"BoundingBox": {"Width": 0.2347254604101181, "Height": 0.02228526957333088, "Left": 0.35775017738342285, "Top": 0.8592377305030823}, "Polygon": [{"X": 0.35775017738342285, "Y": 0.8679602742195129}, {"X": 0.5920996069908142, "Y": 0.8592377305030823}, {"X": 0.5924756526947021, "Y": 0.8728010654449463}, {"X": 0.3583308160305023, "Y": 0.8815230131149292}]}, "Id": "8e037723-dd9c-44e7-ad91-7c5e4c62c91f", "Relationships": [{"Type": "CHILD", "Ids": ["6e1f94c6-6d42-4c72-9726-dd72c014637d", "42ddd04f-1ec3-41e9-8f29-bad2affae7df", "78be5f76-f6a6-4244-a96c-e485029d8525"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056037452071905136, "Height": 0.016172492876648903, "Left": 0.36072900891304016, "Top": 0.37474048137664795}, "Polygon": [{"X": 0.36072900891304016, "Y": 0.37674590945243835}, {"X": 0.4162302613258362, "Y": 0.37474048137664795}, {"X": 0.4167664647102356, "Y": 0.3889075517654419}, {"X": 0.3613143265247345, "Y": 0.3909129798412323}]}, "Id": "cd4cb3bb-ae49-47bb-b2d6-979459ebad0d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056412920355796814, "Height": 0.016160478815436363, "Left": 0.4302762448787689, "Top": 0.37340250611305237}, "Polygon": [{"X": 0.4302762448787689, "Y": 0.37542393803596497}, {"X": 0.486215740442276, "Y": 0.37340250611305237}, {"X": 0.48668915033340454, "Y": 0.38754159212112427}, {"X": 0.43079903721809387, "Y": 0.3895629942417145}]}, "Id": "654bea73-97fd-42e8-a29c-8bf92f073afd"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "#674", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05677762255072594, "Height": 0.01606944017112255, "Left": 0.5015188455581665, "Top": 0.37207967042922974}, "Polygon": [{"X": 0.5015188455581665, "Y": 0.37411683797836304}, {"X": 0.5578893423080444, "Y": 0.37207967042922974}, {"X": 0.5582964420318604, "Y": 0.3861120045185089}, {"X": 0.5019752979278564, "Y": 0.38814911246299744}]}, "Id": "3750ed45-68bd-4cc6-9e27-196ecb53f5ae"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "6107", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054580312222242355, "Height": 0.016072213649749756, "Left": 0.3908270299434662, "Top": 0.3950906991958618}, "Polygon": [{"X": 0.3908270299434662, "Y": 0.3970470130443573}, {"X": 0.4448976516723633, "Y": 0.3950906991958618}, {"X": 0.44540736079216003, "Y": 0.4092066287994385}, {"X": 0.39138445258140564, "Y": 0.4111629128456116}]}, "Id": "f0efd540-ba35-4e23-9c27-80408d183346"}, {"BlockType": "WORD", "Confidence": 99.33294677734375, "Text": "3M", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027121642604470253, "Height": 0.014809180982410908, "Left": 0.46118026971817017, "Top": 0.39473244547843933}, "Polygon": [{"X": 0.46118026971817017, "Y": 0.3956970274448395}, {"X": 0.48783913254737854, "Y": 0.39473244547843933}, {"X": 0.4883019030094147, "Y": 0.408577024936676}, {"X": 0.4616661071777344, "Y": 0.40954160690307617}]}, "Id": "e9023b8f-53aa-498f-8261-44e16942e355"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "DR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027008796110749245, "Height": 0.014382705092430115, "Left": 0.5034638047218323, "Top": 0.3939698040485382}, "Polygon": [{"X": 0.5034638047218323, "Y": 0.39493218064308167}, {"X": 0.5300594568252563, "Y": 0.3939698040485382}, {"X": 0.5304726362228394, "Y": 0.40739017724990845}, {"X": 0.503899335861206, "Y": 0.4083525240421295}]}, "Id": "6f5a11c4-8616-4e55-88a2-6ab5999ba004"}, {"BlockType": "WORD", "Confidence": 97.55958557128906, "Text": "MENOMONIE,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13841810822486877, "Height": 0.023221712559461594, "Left": 0.36184778809547424, "Top": 0.4136582016944885}, "Polygon": [{"X": 0.36184778809547424, "Y": 0.41865092515945435}, {"X": 0.49966931343078613, "Y": 0.4136582016944885}, {"X": 0.500265896320343, "Y": 0.43188729882240295}, {"X": 0.3626016080379486, "Y": 0.4368799030780792}]}, "Id": "30775624-ee62-40f4-8135-a8794cdb6d89"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028730962425470352, "Height": 0.01432209275662899, "Left": 0.5165271759033203, "Top": 0.4144487977027893}, "Polygon": [{"X": 0.5165271759033203, "Y": 0.41547536849975586}, {"X": 0.5448605418205261, "Y": 0.4144487977027893}, {"X": 0.545258104801178, "Y": 0.4277443587779999}, {"X": 0.5169483423233032, "Y": 0.42877089977264404}]}, "Id": "db17f85b-322c-4f8d-94ec-4298b411102b"}, {"BlockType": "WORD", "Confidence": 99.57210540771484, "Text": "Invoice#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11098285019397736, "Height": 0.018895041197538376, "Left": 0.3254137635231018, "Top": 0.4565630555152893}, "Polygon": [{"X": 0.3254137635231018, "Y": 0.46057361364364624}, {"X": 0.4358486533164978, "Y": 0.4565630555152893}, {"X": 0.43639659881591797, "Y": 0.4714476466178894}, {"X": 0.3260648846626282, "Y": 0.4754580855369568}]}, "Id": "5b8d5b9b-8fb7-4df7-9c8f-1974118c2558"}, {"BlockType": "WORD", "Confidence": 98.15669250488281, "Text": "0091264", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09673810005187988, "Height": 0.017570653930306435, "Left": 0.46393021941185, "Top": 0.45412376523017883}, "Polygon": [{"X": 0.46393021941185, "Y": 0.4576224982738495}, {"X": 0.5602600574493408, "Y": 0.45412376523017883}, {"X": 0.5606683492660522, "Y": 0.46819573640823364}, {"X": 0.4644235372543335, "Y": 0.4716944098472595}]}, "Id": "9171f4d1-06b1-41a6-9532-420c4800470f"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Trans", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07099373638629913, "Height": 0.016604751348495483, "Left": 0.32348528504371643, "Top": 0.47889256477355957}, "Polygon": [{"X": 0.32348528504371643, "Y": 0.48145386576652527}, {"X": 0.393924355506897, "Y": 0.47889256477355957}, {"X": 0.39447900652885437, "Y": 0.49293607473373413}, {"X": 0.32410213351249695, "Y": 0.49549731612205505}]}, "Id": "abe293b6-4fca-48be-91a2-1773300f730f"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015050084330141544, "Height": 0.013492811471223831, "Left": 0.4224761724472046, "Top": 0.47850680351257324}, "Polygon": [{"X": 0.4224761724472046, "Y": 0.47903674840927124}, {"X": 0.43704935908317566, "Y": 0.47850680351257324}, {"X": 0.43752625584602356, "Y": 0.49146968126296997}, {"X": 0.4229649305343628, "Y": 0.4919995963573456}]}, "Id": "d82453a5-ea48-4723-95ab-8875a0410061"}, {"BlockType": "WORD", "Confidence": 94.86766815185547, "Text": "0900-1264", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12322931736707687, "Height": 0.018567979335784912, "Left": 0.4652111232280731, "Top": 0.473512202501297}, "Polygon": [{"X": 0.4652111232280731, "Y": 0.47797951102256775}, {"X": 0.588055431842804, "Y": 0.473512202501297}, {"X": 0.5884404182434082, "Y": 0.4876129627227783}, {"X": 0.46570494771003723, "Y": 0.4920801818370819}]}, "Id": "4090b750-5cf0-4200-8598-45c933a6bd8b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05476602539420128, "Height": 0.015079988166689873, "Left": 0.326709121465683, "Top": 0.4999234676361084}, "Polygon": [{"X": 0.326709121465683, "Y": 0.5018981099128723}, {"X": 0.38094615936279297, "Y": 0.4999234676361084}, {"X": 0.38147515058517456, "Y": 0.5130288600921631}, {"X": 0.3272828459739685, "Y": 0.5150034427642822}]}, "Id": "d5abf89b-11b9-47f9-8b54-4a454c02dfc1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "10/14/2024", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13732492923736572, "Height": 0.020604457706212997, "Left": 0.4663439691066742, "Top": 0.49336540699005127}, "Polygon": [{"X": 0.4663439691066742, "Y": 0.4983505308628082}, {"X": 0.6032568216323853, "Y": 0.49336540699005127}, {"X": 0.6036689281463623, "Y": 0.5089849233627319}, {"X": 0.4668905735015869, "Y": 0.513969898223877}]}, "Id": "398e1316-e25b-46ed-b434-f6d0ff813191"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05644448474049568, "Height": 0.01606922037899494, "Left": 0.32618141174316406, "Top": 0.5192623734474182}, "Polygon": [{"X": 0.32618141174316406, "Y": 0.5212991237640381}, {"X": 0.382059782743454, "Y": 0.5192623734474182}, {"X": 0.38262587785720825, "Y": 0.5332948565483093}, {"X": 0.3267969489097595, "Y": 0.5353315472602844}]}, "Id": "9d55f0f1-954a-41b1-9d42-b2d19c83a7ca"}, {"BlockType": "WORD", "Confidence": 98.8243408203125, "Text": "04:31", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06487687677145004, "Height": 0.015986574813723564, "Left": 0.46685606241226196, "Top": 0.516070544719696}, "Polygon": [{"X": 0.46685606241226196, "Y": 0.5184202790260315}, {"X": 0.5313109159469604, "Y": 0.516070544719696}, {"X": 0.5317329168319702, "Y": 0.5297074913978577}, {"X": 0.46733343601226807, "Y": 0.5320571660995483}]}, "Id": "25ccc348-01b1-41ff-a922-8ebb999c33c2"}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02672739513218403, "Height": 0.014066804200410843, "Left": 0.5499703884124756, "Top": 0.515281617641449}, "Polygon": [{"X": 0.5499703884124756, "Y": 0.5162426233291626}, {"X": 0.5763292908668518, "Y": 0.515281617641449}, {"X": 0.5766977667808533, "Y": 0.5283874869346619}, {"X": 0.5503605604171753, "Y": 0.5293484330177307}]}, "Id": "f9a540bf-80ac-4b9e-a754-8996dc740866"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05492456257343292, "Height": 0.019047394394874573, "Left": 0.32986927032470703, "Top": 0.5603500604629517}, "Polygon": [{"X": 0.32986927032470703, "Y": 0.5623319149017334}, {"X": 0.38410577178001404, "Y": 0.5603500604629517}, {"X": 0.38479381799697876, "Y": 0.5774156451225281}, {"X": 0.3306158185005188, "Y": 0.579397439956665}]}, "Id": "fb55b050-8cea-43d5-a6be-7d02c636c852"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11002308875322342, "Height": 0.02181054838001728, "Left": 0.3995164632797241, "Top": 0.5559906363487244}, "Polygon": [{"X": 0.3995164632797241, "Y": 0.5599899291992188}, {"X": 0.5089618563652039, "Y": 0.5559906363487244}, {"X": 0.5095395445823669, "Y": 0.5738019943237305}, {"X": 0.4002172350883484, "Y": 0.5778011679649353}]}, "Id": "a5c63447-9ff2-485c-b043-244afbefc831"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0690527930855751, "Height": 0.017594754695892334, "Left": 0.5374618768692017, "Top": 0.5537905097007751}, "Polygon": [{"X": 0.5374618768692017, "Y": 0.5562995672225952}, {"X": 0.6061179041862488, "Y": 0.5537905097007751}, {"X": 0.6065146923065186, "Y": 0.568876326084137}, {"X": 0.5379241108894348, "Y": 0.5713852643966675}]}, "Id": "9ba695ee-1fa3-4718-80fb-b900f34c38bb"}, {"BlockType": "WORD", "Confidence": 99.52168273925781, "Text": "04", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02663435973227024, "Height": 0.014045603573322296, "Left": 0.3448355197906494, "Top": 0.5812066197395325}, "Polygon": [{"X": 0.3448355197906494, "Y": 0.5821613669395447}, {"X": 0.3709304630756378, "Y": 0.5812066197395325}, {"X": 0.3714698553085327, "Y": 0.5942975282669067}, {"X": 0.3453965187072754, "Y": 0.5952522158622742}]}, "Id": "d0c531af-3cd9-447b-b5b6-af77a2cf964f"}, {"BlockType": "WORD", "Confidence": 99.56233978271484, "Text": "171.800", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09644929319620132, "Height": 0.017138605937361717, "Left": 0.41411861777305603, "Top": 0.5767549276351929}, "Polygon": [{"X": 0.41411861777305603, "Y": 0.5802676677703857}, {"X": 0.5101263523101807, "Y": 0.5767549276351929}, {"X": 0.5105679035186768, "Y": 0.5903809070587158}, {"X": 0.4146428406238556, "Y": 0.5938935279846191}]}, "Id": "f14a1c97-4180-427c-a2a0-74c649d0340d"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "3.589", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06846767663955688, "Height": 0.017491890117526054, "Left": 0.5388782024383545, "Top": 0.5742992758750916}, "Polygon": [{"X": 0.5388782024383545, "Y": 0.5767900943756104}, {"X": 0.6069515347480774, "Y": 0.5742992758750916}, {"X": 0.6073458790779114, "Y": 0.5893003940582275}, {"X": 0.5393370389938354, "Y": 0.5917911529541016}]}, "Id": "684c953d-64d6-4477-9892-0060a680d1ca"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "04", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02648155763745308, "Height": 0.014114859513938427, "Left": 0.34591373801231384, "Top": 0.6015783548355103}, "Polygon": [{"X": 0.34591373801231384, "Y": 0.6025285720825195}, {"X": 0.37185290455818176, "Y": 0.6015783548355103}, {"X": 0.37239527702331543, "Y": 0.6147430539131165}, {"X": 0.3464777171611786, "Y": 0.615693211555481}]}, "Id": "2c680210-4600-423d-b9e5-b5db2e719bb0"}, {"BlockType": "WORD", "Confidence": 99.61355590820312, "Text": "20.692", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08186110109090805, "Height": 0.01677897572517395, "Left": 0.4293588399887085, "Top": 0.5970683097839355}, "Polygon": [{"X": 0.4293588399887085, "Y": 0.6000507473945618}, {"X": 0.5107728838920593, "Y": 0.5970683097839355}, {"X": 0.5112199187278748, "Y": 0.6108649969100952}, {"X": 0.42987701296806335, "Y": 0.6138473153114319}]}, "Id": "6870cb8c-3e6e-470a-b4ec-8c5213b60485"}, {"BlockType": "WORD", "Confidence": 99.71360778808594, "Text": "4.189", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06841839104890823, "Height": 0.01738291047513485, "Left": 0.5398927330970764, "Top": 0.5947299003601074}, "Polygon": [{"X": 0.5398927330970764, "Y": 0.5972221493721008}, {"X": 0.6079201102256775, "Y": 0.5947299003601074}, {"X": 0.608311116695404, "Y": 0.6096206903457642}, {"X": 0.5403478145599365, "Y": 0.612112820148468}]}, "Id": "2f350091-458c-4846-b688-138af2393c76"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09675811976194382, "Height": 0.017069604247808456, "Left": 0.33332645893096924, "Top": 0.6404123306274414}, "Polygon": [{"X": 0.33332645893096924, "Y": 0.6439467668533325}, {"X": 0.429575115442276, "Y": 0.6404123306274414}, {"X": 0.43008458614349365, "Y": 0.6539475917816162}, {"X": 0.33391866087913513, "Y": 0.6574819087982178}]}, "Id": "6a5392f6-e0cf-4327-8dd6-43f94081efa4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08349441736936569, "Height": 0.0188782699406147, "Left": 0.5274695754051208, "Top": 0.6347059011459351}, "Polygon": [{"X": 0.5274695754051208, "Y": 0.6377570033073425}, {"X": 0.6105499863624573, "Y": 0.6347059011459351}, {"X": 0.6109640002250671, "Y": 0.6505332589149475}, {"X": 0.5279670357704163, "Y": 0.6535841822624207}]}, "Id": "fb6992c0-afab-4f45-b742-53ed04a59a5d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DSL2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05495951697230339, "Height": 0.01497382391244173, "Left": 0.33432072401046753, "Top": 0.6626691818237305}, "Polygon": [{"X": 0.33432072401046753, "Y": 0.6646707653999329}, {"X": 0.3887576460838318, "Y": 0.6626691818237305}, {"X": 0.3892802298069, "Y": 0.6756415367126465}, {"X": 0.334888219833374, "Y": 0.6776430010795593}]}, "Id": "8f374f49-c1f2-48a1-b412-9bfd47c0cffb"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "616.59", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08259709924459457, "Height": 0.01827443577349186, "Left": 0.5286075472831726, "Top": 0.6554474830627441}, "Polygon": [{"X": 0.5286075472831726, "Y": 0.6584699153900146}, {"X": 0.6108053922653198, "Y": 0.6554474830627441}, {"X": 0.6112046241760254, "Y": 0.6706995964050293}, {"X": 0.5290864109992981, "Y": 0.6737219095230103}]}, "Id": "b3e20d75-cc70-488d-ae3b-0bd72561b3bc"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "DEFBULK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09657197445631027, "Height": 0.016623321920633316, "Left": 0.33505934476852417, "Top": 0.6816321015357971}, "Polygon": [{"X": 0.33505934476852417, "Y": 0.6851691603660583}, {"X": 0.4311387538909912, "Y": 0.6816321015357971}, {"X": 0.43163132667541504, "Y": 0.6947185397148132}, {"X": 0.33563196659088135, "Y": 0.6982554793357849}]}, "Id": "a78b5ba1-846b-4081-9c5f-dea7fb7f6acf"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "86.68", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06907442212104797, "Height": 0.016308285295963287, "Left": 0.5434471964836121, "Top": 0.6766374111175537}, "Polygon": [{"X": 0.5434471964836121, "Y": 0.6791672706604004}, {"X": 0.6121616363525391, "Y": 0.6766374111175537}, {"X": 0.6125215888023376, "Y": 0.6904159188270569}, {"X": 0.5438673496246338, "Y": 0.692945659160614}]}, "Id": "e8bb0228-35d6-4161-988a-fd894913541e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06977012008428574, "Height": 0.015891438350081444, "Left": 0.33563369512557983, "Top": 0.7233551144599915}, "Polygon": [{"X": 0.33563369512557983, "Y": 0.725910484790802}, {"X": 0.404878169298172, "Y": 0.7233551144599915}, {"X": 0.4054037928581238, "Y": 0.7366912961006165}, {"X": 0.3362182676792145, "Y": 0.7392465472221375}]}, "Id": "24235ba0-131d-4a5f-bc90-0d06839d865f"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "703.27", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08198404312133789, "Height": 0.01752222515642643, "Left": 0.5179861187934875, "Top": 0.7172594666481018}, "Polygon": [{"X": 0.5179861187934875, "Y": 0.7202706933021545}, {"X": 0.5995784401893616, "Y": 0.7172594666481018}, {"X": 0.5999701619148254, "Y": 0.7317705750465393}, {"X": 0.5184533596038818, "Y": 0.7347816824913025}]}, "Id": "95c8ab09-7222-418a-99bc-e671152efe3a"}, {"BlockType": "WORD", "Confidence": 99.51908874511719, "Text": "NBS/KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11086464673280716, "Height": 0.01949417218565941, "Left": 0.3385973572731018, "Top": 0.7623836994171143}, "Polygon": [{"X": 0.3385973572731018, "Y": 0.7664640545845032}, {"X": 0.4488963186740875, "Y": 0.7623836994171143}, {"X": 0.44946199655532837, "Y": 0.7777976989746094}, {"X": 0.3392718434333801, "Y": 0.781877875328064}]}, "Id": "a0f925bd-976f-46ef-a4fe-520f7d3f423e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05551594868302345, "Height": 0.01500790473073721, "Left": 0.46281370520591736, "Top": 0.7604841589927673}, "Polygon": [{"X": 0.46281370520591736, "Y": 0.7625224590301514}, {"X": 0.517910897731781, "Y": 0.7604841589927673}, {"X": 0.5183296203613281, "Y": 0.7734538316726685}, {"X": 0.4632781445980072, "Y": 0.7754920125007629}]}, "Id": "fbb982b6-3b8d-4249-95cd-3a1712a01c90"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06804925203323364, "Height": 0.015637317672371864, "Left": 0.****************, "Top": 0.7580264806747437}, "Polygon": [{"X": 0.****************, "Y": 0.760530948638916}, {"X": 0.6013321280479431, "Y": 0.7580264806747437}, {"X": 0.6016861200332642, "Y": 0.7711594104766846}, {"X": 0.****************, "Y": 0.7736637592315674}]}, "Id": "a5d93a03-eef9-4079-a9d8-e34630743659"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06994561851024628, "Height": 0.015488600358366966, "Left": 0.33996036648750305, "Top": 0.7845820188522339}, "Polygon": [{"X": 0.33996036648750305, "Y": 0.7871540188789368}, {"X": 0.****************, "Y": 0.7845820188522339}, {"X": 0.****************, "Y": 0.7974987626075745}, {"X": 0.34052520990371704, "Y": 0.800070583820343}]}, "Id": "60703bd1-4ffb-4d98-bc07-800b4694e8fc"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03840961307287216, "Height": 0.014294314198195934, "Left": 0.****************, "Top": 0.7827568054199219}, "Polygon": [{"X": 0.****************, "Y": 0.7841624021530151}, {"X": 0.****************, "Y": 0.7827568054199219}, {"X": 0.****************, "Y": 0.7956455945968628}, {"X": 0.****************, "Y": 0.7970511317253113}]}, "Id": "bbb6d481-4a19-44a9-8e54-f4bcf573182f"}, {"BlockType": "WORD", "Confidence": 28.0056209564209, "Text": "XXXXXXXXXXXXXX1591", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24606989324092865, "Height": 0.021812358871102333, "Left": 0.3405051827430725, "Top": 0.7990379929542542}, "Polygon": [{"X": 0.3405051827430725, "Y": 0.8081507086753845}, {"X": 0.5862196087837219, "Y": 0.7990379929542542}, {"X": 0.5865750908851624, "Y": 0.8117382526397705}, {"X": 0.3410608470439911, "Y": 0.8208503723144531}]}, "Id": "74993a48-2b44-4509-ad26-a5e01908ea2a"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05482092872262001, "Height": 0.01495266892015934, "Left": 0.341703325510025, "Top": 0.8259204030036926}, "Polygon": [{"X": 0.341703325510025, "Y": 0.827936589717865}, {"X": 0.3960036635398865, "Y": 0.8259204030036926}, {"X": 0.39652425050735474, "Y": 0.8388569951057434}, {"X": 0.34226906299591064, "Y": 0.8408730626106262}]}, "Id": "31ae62b0-46b5-4346-8ed6-4b8a09cab829"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01531685795634985, "Height": 0.013441907241940498, "Left": 0.4104309380054474, "Top": 0.8245274424552917}, "Polygon": [{"X": 0.4104309380054474, "Y": 0.8250777721405029}, {"X": 0.4252532124519348, "Y": 0.8245274424552917}, {"X": 0.4257477819919586, "Y": 0.8374190330505371}, {"X": 0.41093775629997253, "Y": 0.8379693627357483}]}, "Id": "a9511491-d204-41d3-ac0a-8e2b4ded3bac"}, {"BlockType": "WORD", "Confidence": 99.5517807006836, "Text": "P02385", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08157550543546677, "Height": 0.015940601006150246, "Left": 0.5366358160972595, "Top": 0.8179303407669067}, "Polygon": [{"X": 0.5366358160972595, "Y": 0.8209468126296997}, {"X": 0.617875337600708, "Y": 0.8179303407669067}, {"X": 0.6182113289833069, "Y": 0.830854594707489}, {"X": 0.5370392203330994, "Y": 0.8338708877563477}]}, "Id": "c12e9136-a264-431b-96b6-6b2d13a14cf2"}, {"BlockType": "WORD", "Confidence": 99.3126220703125, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12110332399606705, "Height": 0.01782366819679737, "Left": 0.34280648827552795, "Top": 0.8438926339149475}, "Polygon": [{"X": 0.34280648827552795, "Y": 0.8483769297599792}, {"X": 0.46343013644218445, "Y": 0.8438926339149475}, {"X": 0.4639098346233368, "Y": 0.8572323322296143}, {"X": 0.3433896601200104, "Y": 0.8617163300514221}]}, "Id": "1e3e79f0-8a94-4b0a-aec4-d366b14d1468"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10926497727632523, "Height": 0.017230810597538948, "Left": 0.4822540581226349, "Top": 0.8392885327339172}, "Polygon": [{"X": 0.4822540581226349, "Y": 0.8433369994163513}, {"X": 0.5911532640457153, "Y": 0.8392885327339172}, {"X": 0.5915189981460571, "Y": 0.8524711728096008}, {"X": 0.48271211981773376, "Y": 0.856519341468811}]}, "Id": "888a7d1e-02d8-4995-84ee-830784704da9"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054728832095861435, "Height": 0.015208445489406586, "Left": 0.3577660918235779, "Top": 0.8663145303726196}, "Polygon": [{"X": 0.3577660918235779, "Y": 0.8683323264122009}, {"X": 0.41197627782821655, "Y": 0.8663145303726196}, {"X": 0.4124949276447296, "Y": 0.8795053958892822}, {"X": 0.3583308160305023, "Y": 0.8815230131149292}]}, "Id": "6e1f94c6-6d42-4c72-9726-dd72c014637d"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06490358710289001, "Height": 0.015272888354957104, "Left": 0.4275889992713928, "Top": 0.8633873462677002}, "Polygon": [{"X": 0.4275889992713928, "Y": 0.8657867908477783}, {"X": 0.49205276370048523, "Y": 0.8633873462677002}, {"X": 0.49249258637428284, "Y": 0.8762609958648682}, {"X": 0.4280822277069092, "Y": 0.878660261631012}]}, "Id": "42ddd04f-1ec3-41e9-8f29-bad2affae7df"}, {"BlockType": "WORD", "Confidence": 98.97421264648438, "Text": "P02385", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0819639191031456, "Height": 0.015991825610399246, "Left": 0.5104948282241821, "Top": 0.8592377305030823}, "Polygon": [{"X": 0.5104948282241821, "Y": 0.8622750639915466}, {"X": 0.5920996069908142, "Y": 0.8592377305030823}, {"X": 0.5924587249755859, "Y": 0.8721923828125}, {"X": 0.5109220147132874, "Y": 0.87522953748703}]}, "Id": "78be5f76-f6a6-4244-a96c-e485029d8525"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "36b24786-fc3a-4c2d-bb81-36a7e5b48de9", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "36b24786-fc3a-4c2d-bb81-36a7e5b48de9", "content-type": "application/x-amz-json-1.1", "content-length": "41213", "date": "Thu, 25 Sep 2025 13:34:20 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "a3eea12e-093a-43a6-afc9-a1162c57d6bb", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 25 Sep 2025 13:34:23 GMT", "content-type": "application/json", "content-length": "1165", "connection": "keep-alive", "x-amzn-requestid": "a3eea12e-093a-43a6-afc9-a1162c57d6bb"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. Text: \"KWIK TRIP #674 ... Invoice# 0091264 ... Pump Quantity Price ... Product Amount ... Total ...\". This looks like a fuel receipt? The content: KWIK TRIP maybe a fuel card transaction, with pump quantity, price, product amounts, totals. This is a fuel receipt (or fuel card receipt). Keywords: Fuel, Pump, Gallons? The page has \"Pump Quantity Price\". It says \"Total 703.27\". This is likely a fuel receipt. According to definitions: fuel_receipt: Receipt for fuel purchase. Keywords: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt. Yes.\n\nAlso could be a fuel card statement, but it's a single page. So doc_type = fuel_receipt.\n\nWe need to output JSON with documents array with one object: page_no 1, doc_type \"fuel_receipt\".\n\nWe don't need to mention other pages. Use classify_logistics_doc_type tool."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"fuel_receipt\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2684, "outputTokens": 222, "totalTokens": 2906}, "metrics": {"latencyMs": 1141}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/99fdc4b6_fuel_receipt.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence, follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n        STRICKLY USE classify_logistics_doc_type TOOL CALL TO GIVE STRUCTURED CLASSIFICATION DATA.\n\n    ## Model Instructions:\n        - PDF/image to text conversion is provided in UserContent.\n        - Examine all keywords present (Along with text) anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination, Keywords indication and Key fields/structure are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice or Freight Bill\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Strickly check again if has some of keywords present in invoice, similar to Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount, etc. then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — \"Devilery Order\" or \"Bill of Lading\"\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received, delivery address.\n        Note: Freight bill number, Bill of ladding number, PO number might be present in header of pod.\n        Footer: signature/date block, condition remarks, received by.\n\n        rate_confirmation — \"Load confirmation\" of \"Carrier Rate Confirmation\"\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n\n        clear_to_pay — Clear to Pay\n        Definition: Approval for payment or ACH/Wire/Check transfer request or approval. Document or an email.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Strict note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item). Document or an email.\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple types of documents on one page, Multiple headers with multiple keywords of different types of documents.\n        \n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation, Order Acknowledgement\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Full out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n\n    ## Response format:\n        {\n            \"documents\": [\n                {\n                    \"page_no\": [actual_page_number_in_int],\n                    \"doc_type\": [actual_document_type_in_string]\n                }\n            ]\n        }\n", "user_prompt": "<page1>\nKWIK TRIP #674\n6107 3M DR\nMENOMONIE, WI\nInvoice# 0091264\nTrans # 0900-1264\nDate\n10/14/2024\nTime\n04:31 PM\nPump Quantity Price\n04\n171.800 3.589\n04\n20.692 4.189\nProduct\nAmount\nDSL2\n616.59\nDEFBULK\n86.68\nTOTAL\n703.27\nNBS/KWIK TRIP FLEET\nCARD# ICR\nXXXXXXXXXXXXXX1591\nAUTH #\nP02385\nRESPONSE: APPROVED\nAUTH REF#: P02385\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "comm_invoice", "lumper_receipt", "bol", "pod", "rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "customs_doc", "other", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 5000}}}