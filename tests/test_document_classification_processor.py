# tests/test_document_classification_processor.py
import sys
import json
from io import By<PERSON><PERSON>
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch, ANY

import pytest
from pypdf import PdfWriter
from PIL import Image

# Ensure project root is on path so imports resolve the same as runtime
project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

from app.services.document_classification_processor import DocumentClassificationProcessor

# --- Fixtures / setup -------------------------------------------------------

@pytest.fixture(autouse=True)
def mock_settings():
    # Patch settings used in the processor (module-level patch)
    with patch('app.services.document_classification_processor.settings') as mock_settings:
        mock_settings.AWS_ACCESS_KEY_ID = "test_access_key"
        mock_settings.AWS_SECRET_ACCESS_KEY = "test_secret_key"
        mock_settings.CLASSIFICATION_FOLDER_OUTPUT_PREFIX = "classification_output"
        mock_settings.SQS_CLASSIFICATION_QUEUE_URL = "unused_queue"
        mock_settings.AWS_REGION = "us-east-1"
        yield mock_settings

@pytest.fixture
def processor():
    return DocumentClassificationProcessor(queue_url="test_queue", region_name="us-east-1")


# --- Initialization tests --------------------------------------------------

def test_init_success(processor):
    assert processor.queue_url == "test_queue"
    assert processor.region_name == "us-east-1"
    assert processor.output_folder == "classification_output"

def test_init_no_queue_url():
    with pytest.raises(ValueError, match="SQS_QUEUE_URL is required."):
        DocumentClassificationProcessor(queue_url="", region_name="us-east-1")


# --- _process_single_message tests -----------------------------------------

@pytest.mark.asyncio
async def test_process_single_message_success(processor):
    message = {
        "ReceiptHandle": "test_handle",
        "Body": json.dumps({
            "Message": json.dumps({
                "Records": [
                    {"s3": {"bucket": {"name": "test_bucket"}, "object": {"key": "test.pdf"}}}
                ]
            })
        })
    }
    sqs_client = AsyncMock()

    with patch.object(processor, '_handle_ingestion', new_callable=AsyncMock, return_value=True) as mock_handle_ingestion, \
         patch.object(processor, '_delete_message', new_callable=AsyncMock) as mock_delete_message:

        await processor._process_single_message(message, sqs_client)

        mock_handle_ingestion.assert_called_once_with("test_bucket", "test.pdf")
        mock_delete_message.assert_called_once_with("test_handle", sqs_client)

@pytest.mark.asyncio
async def test_process_single_message_invalid_format(processor):
    message = {"ReceiptHandle": "test_handle", "Body": json.dumps({"Message": json.dumps({})})}
    sqs_client = AsyncMock()

    with patch.object(processor, '_delete_message', new_callable=AsyncMock) as mock_delete_message:
        await processor._process_single_message(message, sqs_client)
        mock_delete_message.assert_called_once_with("test_handle", sqs_client)

@pytest.mark.asyncio
async def test_process_single_message_ingestion_fails(processor):
    message = {
        "ReceiptHandle": "test_handle",
        "Body": json.dumps({
            "Message": json.dumps({
                "Records": [
                    {"s3": {"bucket": {"name": "test_bucket"}, "object": {"key": "test.pdf"}}}
                ]
            })
        })
    }
    sqs_client = AsyncMock()

    with patch.object(processor, '_handle_ingestion', new_callable=AsyncMock, return_value=False) as mock_handle_ingestion, \
         patch.object(processor, '_delete_message', new_callable=AsyncMock) as mock_delete_message:

        await processor._process_single_message(message, sqs_client)

        mock_handle_ingestion.assert_called_once()
        mock_delete_message.assert_not_called()

@pytest.mark.asyncio
async def test_process_single_message_invalid_json_body(processor):
    message = {"ReceiptHandle": "test_handle", "Body": "not a json"}
    sqs_client = AsyncMock()

    with patch.object(processor, '_delete_message', new_callable=AsyncMock) as mock_delete_message:
        # should catch JSON error and not delete
        await processor._process_single_message(message, sqs_client)
        mock_delete_message.assert_not_called()


# --- _handle_ingestion (pdf) ------------------------------------------------

@pytest.mark.asyncio
@patch('app.services.document_classification_processor.ai_classification_service', new_callable=AsyncMock)
@patch('app.services.document_classification_processor.s3_service', new_callable=MagicMock)
async def test_handle_ingestion_pdf_success(mock_s3_service, mock_ai_service, processor):
    # AI returns pages mapping
    mock_ai_service.return_value = {"classification_result": {"documents": [{"doc_type": "invoice", "page_no": 1}]}}
    # Create a dummy PDF with one blank page
    pdf_writer = PdfWriter()
    pdf_writer.add_blank_page(width=100, height=100)
    pdf_bytes = BytesIO()
    pdf_writer.write(pdf_bytes)
    pdf_bytes.seek(0)

    # s3_service.download_file returns file-like object usable by PdfReader
    mock_s3_service.download_file = AsyncMock(return_value=BytesIO(pdf_bytes.getvalue()))
    mock_s3_service.upload_file_from_bytes = AsyncMock(return_value=True)

    result = await processor._handle_ingestion("test_bucket", "input/classification-input/test.pdf")

    assert result is True

    # verify uploads: invoice.pdf and classification.json called
    calls = [c[1] for c in mock_s3_service.upload_file_from_bytes.call_args_list]
    object_keys = [call['object_key'] for call in calls]
    assert f"classification_output/test/invoice.pdf" in object_keys
    assert f"classification_output/test/classification.json" in object_keys

@pytest.mark.asyncio
@patch('app.services.document_classification_processor.ai_classification_service', new_callable=AsyncMock)
@patch('app.services.document_classification_processor.s3_service', new_callable=MagicMock)
async def test_handle_ingestion_image_success(mock_s3_service, mock_ai_service, processor):
    mock_ai_service.return_value = {"classification_result": {"documents": [{"doc_type": "photo", "page_no": 1}]}}
    # Create a dummy JPEG image
    img = Image.new('RGB', (50, 50), color='blue')
    img_bytes = BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)

    mock_s3_service.download_file = AsyncMock(return_value=BytesIO(img_bytes.getvalue()))
    mock_s3_service.upload_file_from_bytes = AsyncMock(return_value=True)

    result = await processor._handle_ingestion("test_bucket", "input/classification-input/test.jpg")

    assert result is True

    # ensure an image output and classification.json were uploaded
    calls = [c[1] for c in mock_s3_service.upload_file_from_bytes.call_args_list]
    object_keys = [call['object_key'] for call in calls]
    assert "classification_output/test/photo.jpg" in object_keys
    assert "classification_output/test/classification.json" in object_keys

@pytest.mark.asyncio
async def test_handle_ingestion_unsupported_file_type(processor):
    with patch('app.services.document_classification_processor.ai_classification_service', new_callable=AsyncMock, return_value={"classification_result": {}}), \
         patch('app.services.document_classification_processor.s3_service.download_file', new_callable=AsyncMock, return_value=BytesIO(b"dummy")):

        with pytest.raises(ValueError, match="Unsupported file type: .txt"):
            await processor._handle_ingestion("test_bucket", "file.txt")

@pytest.mark.asyncio
async def test_handle_ingestion_ai_service_fails(processor):
    with patch('app.services.document_classification_processor.ai_classification_service', new_callable=AsyncMock, side_effect=Exception("AI fail")):
        with pytest.raises(Exception, match="AI fail"):
            await processor._handle_ingestion("test_bucket", "input/classification-input/test.pdf")

@pytest.mark.asyncio
async def test_handle_ingestion_uploads_fail(processor):
    with patch('app.services.document_classification_processor.ai_classification_service',
               new_callable=AsyncMock,
               return_value={"classification_result": {"documents": []}}), \
         patch('app.services.document_classification_processor.s3_service.download_file',
               new_callable=AsyncMock, return_value=BytesIO(b"dummy")), \
         patch.object(processor, '_group_documents_by_type', return_value={}), \
         patch.object(processor, '_upload_grouped_docs', new_callable=AsyncMock, return_value=[]):

        result = await processor._handle_ingestion("test_bucket", "input/classification-input/test.pdf")
        assert result is False


# --- grouping logic tests --------------------------------------------------

def test_group_documents_pdf(processor):
    pdf_writer = PdfWriter()
    pdf_writer.add_blank_page(width=100, height=100)
    pdf_bytes = BytesIO()
    pdf_writer.write(pdf_bytes)
    pdf_bytes.seek(0)

    classification = {"documents": [{"doc_type": "invoice", "page_no": 1}]}

    grouped = processor._group_documents_by_type(pdf_bytes, ".pdf", classification)

    assert "invoice" in grouped
    assert isinstance(grouped["invoice"], PdfWriter)
    assert len(grouped["invoice"].pages) == 1

def test_group_documents_image(processor):
    img = Image.new('RGB', (64, 64), color='green')
    img_bytes = BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)

    classification = {"documents": [{"doc_type": "photo", "page_no": 1}]}

    grouped = processor._group_documents_by_type(img_bytes, ".png", classification)

    assert "photo" in grouped
    assert isinstance(grouped["photo"], list)
    assert hasattr(grouped["photo"][0], 'copy')

def test_group_documents_tiff(processor):
    # Construct a simple multi-frame TIFF in memory (2 identical frames)
    img = Image.new('RGB', (10, 10), color='yellow')
    buf = BytesIO()
    img.save(buf, format='TIFF', save_all=True, append_images=[img])
    buf.seek(0)

    classification = {"documents": [{"doc_type": "doc1", "page_no": 1}, {"doc_type": "doc1", "page_no": 2}]}
    grouped = processor._group_documents_by_type(buf, ".tiff", classification)

    assert "doc1" in grouped
    assert isinstance(grouped["doc1"], list)
    assert len(grouped["doc1"]) >= 2

def test_group_documents_unsupported_extension(processor):
    with pytest.raises(ValueError, match="Unsupported file type: .txt"):
        processor._group_documents_by_type(BytesIO(), ".txt", {})


# --- output path tests -----------------------------------------------------

def test_build_output_folder_simple_key(processor):
    assert processor._build_output_folder("file.pdf") == "classification_output/file"

def test_build_output_folder_nested_key(processor):
    assert processor._build_output_folder("uploads/customer/2023/file.pdf") == "classification_output/2023/file"


# --- upload helpers tests --------------------------------------------------

@pytest.mark.asyncio
async def test_upload_grouped_docs_pdf(processor):
    pdf_writer = PdfWriter()
    pdf_writer.add_blank_page(width=100, height=100)
    grouped_docs = {"invoice": pdf_writer}

    with patch('app.services.document_classification_processor.s3_service.upload_file_from_bytes', new_callable=AsyncMock, return_value=True) as mock_upload:
        uploaded = await processor._upload_grouped_docs("bucket", grouped_docs, ".pdf", "output/folder")
        assert uploaded == ["output/folder/invoice.pdf"]
        mock_upload.assert_called_once()

@pytest.mark.asyncio
async def test_upload_grouped_docs_image(processor):
    img = Image.new('RGB', (32, 32), color='purple')
    grouped_docs = {"photo": [img]}

    with patch('app.services.document_classification_processor.s3_service.upload_file_from_bytes', new_callable=AsyncMock, return_value=True) as mock_upload:
        uploaded = await processor._upload_grouped_docs("bucket", grouped_docs, ".jpg", "output/folder")
        assert uploaded == ["output/folder/photo.jpg"]
        mock_upload.assert_called()

@pytest.mark.asyncio
async def test_upload_classification_json(processor):
    with patch('app.services.document_classification_processor.s3_service.upload_file_from_bytes', new_callable=AsyncMock, return_value=True) as mock_upload:
        result = await processor._upload_classification_json("bucket", "key", "s3_uri", {"documents": []}, "folder", ["a", "b"])
        assert result is True
        mock_upload.assert_called_once()
        assert mock_upload.call_args[1]['object_key'] == "folder/classification.json"
