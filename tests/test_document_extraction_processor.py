import sys
import json
import asyncio
from io import Bytes<PERSON>
from pathlib import Path
from unittest.mock import AsyncMock, patch

import pytest

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

from app.services.document_extraction_processor import DocumentExtractionProcessor
from botocore.exceptions import ClientError


# -----------------------------
# Fixtures
# -----------------------------
@pytest.fixture(autouse=True)
def mock_settings():
    with patch('app.services.document_extraction_processor.settings') as mock:
        mock.AWS_ACCESS_KEY_ID = "test_access_key"
        mock.AWS_SECRET_ACCESS_KEY = "test_secret_key"
        mock.EXTRACTION_FOLDER_OUTPUT_PREFIX = "extraction_output"
        yield mock


@pytest.fixture
def processor():
    return DocumentExtractionProcessor(queue_url="test_queue", region_name="us-east-1")


# -----------------------------
# Initialization
# -----------------------------
def test_init_success(processor):
    assert processor.queue_url == "test_queue"
    assert processor.region_name == "us-east-1"


def test_init_no_queue_url():
    with pytest.raises(ValueError, match="SQS_QUEUE_URL is required."):
        DocumentExtractionProcessor(queue_url="", region_name="us-east-1")


# -----------------------------
# Message Processing
# -----------------------------
@pytest.mark.asyncio
async def test_process_single_message_success(processor):
    message = {
        "ReceiptHandle": "test_handle",
        "Body": json.dumps({
            "Message": json.dumps({"Records": [
                {"s3": {"bucket": {"name": "test_bucket"}, "object": {"key": "input/test.pdf"}}}
            ]})
        })
    }
    sqs_client = AsyncMock()

    with patch.object(processor, '_handle_ingestion', new_callable=AsyncMock, return_value=True) as mock_ingestion, \
         patch.object(processor, '_delete_message', new_callable=AsyncMock) as mock_delete:
        
        await processor._process_single_message(message, sqs_client)
        mock_ingestion.assert_called_once_with("test_bucket", "input/test.pdf")
        mock_delete.assert_called_once_with("test_handle", sqs_client)


@pytest.mark.asyncio
async def test_process_single_message_invalid_message(processor):
    message = {"ReceiptHandle": "test_handle", "Body": json.dumps({"Message": json.dumps({})})}
    sqs_client = AsyncMock()

    with patch.object(processor, '_delete_message', new_callable=AsyncMock) as mock_delete:
        await processor._process_single_message(message, sqs_client)
        mock_delete.assert_called_once_with("test_handle", sqs_client)


@pytest.mark.asyncio
async def test_process_single_message_ingestion_fail(processor):
    message = {
        "ReceiptHandle": "test_handle",
        "Body": json.dumps({
            "Message": json.dumps({"Records": [
                {"s3": {"bucket": {"name": "test_bucket"}, "object": {"key": "input/test.pdf"}}}
            ]})
        })
    }
    sqs_client = AsyncMock()

    with patch.object(processor, '_handle_ingestion', new_callable=AsyncMock, return_value=False) as mock_ingestion, \
         patch.object(processor, '_delete_message', new_callable=AsyncMock) as mock_delete:
        
        await processor._process_single_message(message, sqs_client)
        mock_ingestion.assert_called_once()
        mock_delete.assert_not_called()


@pytest.mark.asyncio
async def test_process_single_message_exception(processor):
    message = {"ReceiptHandle": "test_handle", "Body": "invalid_json"}
    sqs_client = AsyncMock()

    with patch.object(processor, '_delete_message', new_callable=AsyncMock) as mock_delete:
        await processor._process_single_message(message, sqs_client)
        mock_delete.assert_not_called()


# -----------------------------
# Ingestion Logic
# -----------------------------
@pytest.mark.asyncio
async def test_handle_ingestion_success(processor):
    with patch('app.services.document_extraction_processor.ai_extraction_service',
               new_callable=AsyncMock,
               return_value={"foo": "bar"}), \
         patch('app.services.document_extraction_processor.s3_service.upload_file_from_bytes',
               new_callable=AsyncMock,
               return_value=True):
        
        result = await processor._handle_ingestion("test_bucket", "input/test.pdf")
        assert result is True


@pytest.mark.asyncio
async def test_handle_ingestion_empty_response(processor):
    with patch('app.services.document_extraction_processor.ai_extraction_service',
               new_callable=AsyncMock,
               return_value={}), \
         patch('app.services.document_extraction_processor.s3_service.upload_file_from_bytes',
               new_callable=AsyncMock):
        
        result = await processor._handle_ingestion("test_bucket", "input/test.pdf")
        assert result is None  # returns early if no response


@pytest.mark.asyncio
async def test_handle_ingestion_upload_fail(processor):
    with patch('app.services.document_extraction_processor.ai_extraction_service',
               new_callable=AsyncMock,
               return_value={"foo": "bar"}), \
         patch('app.services.document_extraction_processor.s3_service.upload_file_from_bytes',
               new_callable=AsyncMock,
               return_value=False):
        
        result = await processor._handle_ingestion("test_bucket", "input/test.pdf")
        assert result is False


@pytest.mark.asyncio
async def test_handle_ingestion_exception(processor):
    with patch('app.services.document_extraction_processor.ai_extraction_service',
               new_callable=AsyncMock,
               side_effect=Exception("AI fail")):
        with pytest.raises(Exception, match="AI fail"):
            await processor._handle_ingestion("test_bucket", "input/test.pdf")


# -----------------------------
# Delete Message
# -----------------------------
@pytest.mark.asyncio
async def test_delete_message_success(processor):
    sqs_client = AsyncMock()
    await processor._delete_message("handle", sqs_client)
    sqs_client.delete_message.assert_awaited_once()


@pytest.mark.asyncio
async def test_delete_message_failure(processor):
    sqs_client = AsyncMock()
    sqs_client.delete_message.side_effect = ClientError(
        {"Error": {"Code": "500", "Message": "fail"}}, "DeleteMessage"
    )
    await processor._delete_message("handle", sqs_client)
    sqs_client.delete_message.assert_awaited_once()
